# K230最简化测试 - 验证正确的API
import time, os, sys

def test_imports():
    """测试导入模块"""
    print("=== 测试K230模块导入 ===")
    
    try:
        from media.sensor import *
        print("✅ from media.sensor import * 成功")
    except ImportError as e:
        print("❌ media.sensor导入失败:", e)
        return False
        
    try:
        from media.display import *
        print("✅ from media.display import * 成功")
    except ImportError as e:
        print("❌ media.display导入失败:", e)
        return False
        
    try:
        from media.media import *
        print("✅ from media.media import * 成功")
    except ImportError as e:
        print("❌ media.media导入失败:", e)
        return False
        
    return True

def test_sensor_creation():
    """测试传感器创建"""
    print("\\n=== 测试传感器创建 ===")
    
    try:
        from media.sensor import *
        
        # 创建传感器对象
        sensor = Sensor(id=2)  # 使用默认摄像头
        print("✅ Sensor(id=2) 创建成功")
        
        # 复位传感器
        sensor.reset()
        print("✅ sensor.reset() 成功")
        
        # 设置帧大小
        sensor.set_framesize(Sensor.QVGA)  # 320x240
        print("✅ sensor.set_framesize() 成功")
        
        # 设置像素格式
        sensor.set_pixformat(Sensor.RGB565)
        print("✅ sensor.set_pixformat() 成功")
        
        return sensor
        
    except Exception as e:
        print("❌ 传感器创建失败:", str(e))
        return None

def test_display_init():
    """测试显示初始化"""
    print("\\n=== 测试显示初始化 ===")
    
    try:
        from media.display import *
        
        # 初始化虚拟显示
        Display.init(Display.VIRT, 320, 240)
        print("✅ Display.init() 成功")
        
        return True
        
    except Exception as e:
        print("❌ 显示初始化失败:", str(e))
        return False

def test_media_manager():
    """测试媒体管理器"""
    print("\\n=== 测试媒体管理器 ===")
    
    try:
        from media.media import *
        
        # 初始化媒体管理器
        MediaManager.init()
        print("✅ MediaManager.init() 成功")
        
        return True
        
    except Exception as e:
        print("❌ 媒体管理器初始化失败:", str(e))
        return False

def test_camera_capture():
    """测试摄像头捕获"""
    print("\\n=== 测试摄像头捕获 ===")
    
    try:
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        # 创建和配置传感器
        sensor = Sensor(id=2)
        sensor.reset()
        sensor.set_framesize(Sensor.QVGA)
        sensor.set_pixformat(Sensor.RGB565)
        
        # 初始化显示和媒体管理器
        Display.init(Display.VIRT, sensor.width(), sensor.height())
        MediaManager.init()
        
        # 启动传感器
        sensor.run()
        print("✅ sensor.run() 成功")
        
        # 等待稳定
        time.sleep(2)
        
        # 尝试捕获图像
        for i in range(5):
            img = sensor.snapshot()
            if img:
                print(f"✅ 第{i+1}次图像捕获成功: {img.width()}x{img.height()}")
                
                # 简单绘制测试
                img.draw_string(10, 10, f"测试 {i+1}/5", color=(255, 255, 255), scale=1)
                img.draw_rectangle((50, 50, 100, 60), color=(255, 0, 0), thickness=2)
                
                # 显示图像
                Display.show_image(img)
                print(f"✅ 第{i+1}次图像显示成功")
            else:
                print(f"❌ 第{i+1}次图像捕获失败")
                
            time.sleep(0.5)
        
        return True
        
    except Exception as e:
        print("❌ 摄像头捕获测试失败:", str(e))
        return False

def simple_color_detection():
    """简单颜色检测"""
    print("\\n=== 简单颜色检测测试 ===")
    
    try:
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        # 初始化
        sensor = Sensor(id=2)
        sensor.reset()
        sensor.set_framesize(Sensor.QVGA)
        sensor.set_pixformat(Sensor.RGB565)
        
        Display.init(Display.VIRT, sensor.width(), sensor.height())
        MediaManager.init()
        sensor.run()
        
        time.sleep(2)
        
        # 红色阈值
        red_threshold = (30, 100, 15, 127, 15, 127)
        
        print("开始红色检测...")
        clock = time.clock()
        
        for i in range(10):
            clock.tick()
            img = sensor.snapshot()
            
            if img:
                # 检测红色
                blobs = img.find_blobs([red_threshold], pixels_threshold=50)
                
                detected_count = len(blobs)
                
                # 绘制检测结果
                for blob in blobs[:2]:  # 最多显示2个
                    img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
                    img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 0), size=5, thickness=2)
                
                # 绘制状态
                img.draw_string(10, 10, f"检测 {i+1}/10", color=(255, 255, 255), scale=1)
                img.draw_string(10, 25, f"红色: {detected_count}个", color=(255, 255, 255), scale=1)
                img.draw_string(10, 40, f"FPS: {clock.fps():.1f}", color=(255, 255, 255), scale=1)
                
                # 如果检测到红色，绘制矩形块
                if detected_count > 0:
                    img.draw_rectangle((250, 10, 40, 20), color=(255, 0, 0), thickness=-1, fill=True)
                    img.draw_rectangle((250, 10, 40, 20), color=(255, 255, 255), thickness=1)
                    img.draw_string(255, 16, "红", color=(255, 255, 255), scale=1)
                    print(f"第{i+1}次检测到{detected_count}个红色对象")
                
                Display.show_image(img)
                
            time.sleep(0.2)
        
        print("✅ 简单颜色检测完成")
        return True
        
    except Exception as e:
        print("❌ 简单颜色检测失败:", str(e))
        return False

def create_test_report():
    """创建测试报告"""
    report = f"""
# K230 CanMV API测试报告

测试时间: {time.localtime()}

## 测试结果总结
本次测试验证了K230 CanMV的正确API使用方式。

## 正确的导入方式
```python
from media.sensor import *    # 传感器模块
from media.display import *   # 显示模块  
from media.media import *     # 媒体模块
```

## 正确的初始化流程
```python
# 1. 创建传感器
sensor = Sensor(id=2)  # 使用默认摄像头

# 2. 配置传感器
sensor.reset()
sensor.set_framesize(Sensor.QVGA)  # 320x240
sensor.set_pixformat(Sensor.RGB565)

# 3. 初始化显示
Display.init(Display.VIRT, sensor.width(), sensor.height())

# 4. 初始化媒体管理器
MediaManager.init()

# 5. 启动传感器
sensor.run()

# 6. 捕获图像
img = sensor.snapshot()
Display.show_image(img)
```

## 推荐使用的文件
1. k230_correct_color_detector.py - 完整版本
2. k230_simple_test.py - 测试版本

## 运行方法
```python
exec(open('k230_correct_color_detector.py').read())
```
"""
    
    try:
        with open('k230_test_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        print("✅ 测试报告已保存: k230_test_report.md")
    except Exception as e:
        print("❌ 保存测试报告失败:", str(e))

def main():
    """主测试函数"""
    print("🚀 K230 CanMV API正确性测试")
    print("基于01Studio官方文档的API测试")
    
    success_count = 0
    total_tests = 5
    
    # 测试1: 导入模块
    if test_imports():
        success_count += 1
    
    # 测试2: 传感器创建
    sensor = test_sensor_creation()
    if sensor:
        success_count += 1
    
    # 测试3: 显示初始化
    if test_display_init():
        success_count += 1
    
    # 测试4: 媒体管理器
    if test_media_manager():
        success_count += 1
    
    # 测试5: 摄像头捕获
    if test_camera_capture():
        success_count += 1
    
    print(f"\\n=== 测试总结 ===")
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！")
        print("开始颜色检测演示...")
        simple_color_detection()
        print("\\n推荐运行完整版本:")
        print("exec(open('k230_correct_color_detector.py').read())")
    elif success_count >= 3:
        print("⚠️ 部分测试通过，可以尝试基本功能")
        simple_color_detection()
    else:
        print("❌ 大部分测试失败")
        print("请检查:")
        print("1. K230固件版本是否正确")
        print("2. 摄像头是否正确连接")
        print("3. CanMV IDE是否正常连接")
    
    # 生成测试报告
    create_test_report()
    
    print("\\n✅ 测试完成")

if __name__ == "__main__":
    main()
