# K230最简测试 - 解决import语法问题
import time, gc

# 模块级别导入
try:
    from media.sensor import *
    from media.display import *
    from media.media import *
    print("✅ 模块导入成功")
    IMPORT_OK = True
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    IMPORT_OK = False

def quick_test():
    """快速测试"""
    if not IMPORT_OK:
        print("❌ 模块未导入，无法测试")
        return False
    
    print("🧪 开始快速测试...")
    
    try:
        # 清理
        try:
            MediaManager.deinit()
        except:
            pass
        try:
            Display.deinit()
        except:
            pass
        gc.collect()
        time.sleep(1)
        
        # 创建传感器
        sensor = Sensor(id=2)
        sensor.reset()
        sensor.set_framesize(Sensor.QQVGA)  # 最小分辨率160x120
        sensor.set_pixformat(Sensor.RGB565)
        
        print("✅ 传感器配置成功")
        
        # 初始化显示
        Display.init(Display.VIRT, sensor.width(), sensor.height())
        print("✅ 显示初始化成功")
        
        # 初始化媒体管理器
        MediaManager.init()
        print("✅ 媒体管理器初始化成功")
        
        # 启动传感器
        sensor.run()
        print("✅ 传感器启动成功")
        
        # 等待稳定
        time.sleep(2)
        
        # 测试捕获
        print("\\n📸 测试图像捕获...")
        for i in range(3):
            img = sensor.snapshot()
            if img:
                print(f"✅ 第{i+1}次捕获成功: {img.width()}x{img.height()}")
                
                # 简单绘制
                img.draw_string(5, 5, f"测试{i+1}", color=(255, 255, 255), scale=1)
                img.draw_rectangle((10, 15, 30, 20), color=(255, 0, 0), thickness=2)
                
                # 显示
                Display.show_image(img)
            else:
                print(f"❌ 第{i+1}次捕获失败")
            
            time.sleep(0.5)
        
        print("\\n✅ 快速测试完成")
        print("传感器工作正常，可以运行完整程序")
        return True
        
    except Exception as e:
        print(f"❌ 快速测试失败: {str(e)}")
        return False

def simple_red_test():
    """简单红色检测测试"""
    if not IMPORT_OK:
        print("❌ 模块未导入，无法测试")
        return False
    
    print("\\n🔴 简单红色检测测试...")
    
    try:
        # 使用已初始化的传感器
        sensor = Sensor(id=2)
        sensor.reset()
        sensor.set_framesize(Sensor.QQVGA)
        sensor.set_pixformat(Sensor.RGB565)
        
        Display.init(Display.VIRT, sensor.width(), sensor.height())
        MediaManager.init()
        sensor.run()
        time.sleep(1)
        
        # 红色阈值
        red_threshold = (30, 100, 15, 127, 15, 127)
        
        print("开始红色检测...")
        for i in range(5):
            img = sensor.snapshot()
            if img:
                # 检测红色
                blobs = img.find_blobs([red_threshold], pixels_threshold=50)
                
                # 绘制结果
                for blob in blobs[:1]:  # 只显示1个
                    img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
                    img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 0), size=3, thickness=2)
                
                # 状态信息
                img.draw_string(5, 5, f"红色检测{i+1}/5", color=(255, 255, 255), scale=1)
                img.draw_string(5, 15, f"发现:{len(blobs)}个", color=(255, 255, 255), scale=1)
                
                # 如果检测到红色，显示红色块
                if blobs:
                    img.draw_rectangle((120, 5, 25, 15), color=(255, 0, 0), thickness=-1, fill=True)
                    img.draw_rectangle((120, 5, 25, 15), color=(255, 255, 255), thickness=1)
                    img.draw_string(125, 9, "红", color=(255, 255, 255), scale=1)
                    print(f"第{i+1}次检测到{len(blobs)}个红色对象")
                
                Display.show_image(img)
            
            time.sleep(0.5)
            gc.collect()
        
        print("✅ 红色检测测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 红色检测测试失败: {str(e)}")
        return False

def show_usage():
    """显示使用说明"""
    print("""
🆘 K230使用说明

如果遇到问题：

1. 运行此测试文件:
   exec(open('k230_minimal_test.py').read())

2. 如果测试通过，运行完整版本:
   exec(open('k230_final_color_detector.py').read())

3. 如果还有问题:
   - 重启K230开发板
   - 重新连接CanMV IDE
   - 检查摄像头连接

常见错误解决:
- "sensor already inited" → 重启开发板
- "import * not at module level" → 已修复
- "no module named media.camera" → 已修复
""")

def main():
    """主函数"""
    print("🔧 K230最简测试工具")
    print("解决import语法和传感器冲突问题")
    
    if not IMPORT_OK:
        print("\\n❌ 模块导入失败")
        print("请检查:")
        print("1. K230固件版本")
        print("2. CanMV IDE连接")
        print("3. 开发板状态")
        show_usage()
        return
    
    print("\\n开始测试...")
    
    # 快速测试
    if quick_test():
        print("\\n基础功能正常，开始红色检测测试...")
        
        # 红色检测测试
        if simple_red_test():
            print("\\n🎉 所有测试通过！")
            print("\\n现在可以运行完整版本:")
            print("exec(open('k230_final_color_detector.py').read())")
        else:
            print("\\n⚠️ 红色检测测试失败，但基础功能正常")
            print("可以尝试运行完整版本")
    else:
        print("\\n❌ 基础测试失败")
        print("建议:")
        print("1. 重启K230开发板")
        print("2. 重新连接CanMV IDE")
        print("3. 检查摄像头连接")
    
    show_usage()

if __name__ == "__main__":
    main()
