import sensor, image, time, lcd
from maix import GPIO
import gc

# 配置文件 - 统一管理所有变量
CONFIG = {
    'LCD_WIDTH': 320,
    'LCD_HEIGHT': 240,
    'SENSOR_WIDTH': 320,
    'SENSOR_HEIGHT': 240,
    'FPS': 30,
    'RECT_THICKNESS': 2,
    'MIN_AREA': 500,  # 最小检测区域
    'COLOR_THRESHOLD': 30,  # 颜色阈值
    'COLORS': {
        'red': [(30, 100, 15, 127, 15, 127), (255, 0, 0)],      # HSV阈值和RGB显示色
        'green': [(40, 80, -70, -10, -0, 30), (0, 255, 0)],
        'blue': [(0, 30, 0, 64, -128, 0), (0, 0, 255)],
        'yellow': [(20, 100, -10, 10, 20, 127), (255, 255, 0)]
    }
}

class ColorDetector:
    def __init__(self):
        self.init_hardware()  # 初始化硬件
        
    def init_hardware(self):
        """初始化摄像头和LCD"""
        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.QVGA)
        sensor.skip_frames(time=2000)
        sensor.set_auto_gain(False)
        sensor.set_auto_whitebal(False)
        lcd.init(freq=15000000)
        lcd.clear()
        
    def detect_color(self, img, color_name):
        """检测指定颜色并返回最大区域"""
        threshold, display_color = CONFIG['COLORS'][color_name]
        blobs = img.find_blobs([threshold], pixels_threshold=CONFIG['MIN_AREA'], area_threshold=CONFIG['MIN_AREA'])
        
        if blobs:
            largest_blob = max(blobs, key=lambda b: b.pixels())  # 找到最大色块
            return largest_blob, display_color
        return None, display_color
        
    def draw_rectangle(self, img, blob, color):
        """在检测到的色块上绘制矩形"""
        img.draw_rectangle(blob.rect(), color=color, thickness=CONFIG['RECT_THICKNESS'])
        img.draw_cross(blob.cx(), blob.cy(), color=color, size=10, thickness=2)  # 绘制中心点
        
    def display_info(self, img, color_name, blob):
        """显示检测信息"""
        if blob:
            info_text = f"{color_name}: ({blob.cx()},{blob.cy()})"
            img.draw_string(10, 10, info_text, color=(255, 255, 255), scale=1)
            
    def run(self):
        """主运行循环"""
        clock = time.clock()
        
        while True:
            clock.tick()
            img = sensor.snapshot()
            
            # 检测所有配置的颜色
            for color_name in CONFIG['COLORS'].keys():
                blob, display_color = self.detect_color(img, color_name)
                if blob:
                    self.draw_rectangle(img, blob, display_color)
                    self.display_info(img, color_name, blob)
                    break  # 只显示第一个检测到的颜色
                    
            # 显示FPS
            fps_text = f"FPS: {clock.fps():.1f}"
            img.draw_string(10, CONFIG['LCD_HEIGHT']-20, fps_text, color=(255, 255, 255), scale=1)
            
            lcd.display(img)
            gc.collect()  # 垃圾回收

def main():
    """主函数"""
    try:
        detector = ColorDetector()
        print("颜色检测启动，支持颜色：", list(CONFIG['COLORS'].keys()))
        detector.run()
    except Exception as e:
        print(f"错误: {e}")
    finally:
        lcd.clear()

if __name__ == "__main__":
    main()
