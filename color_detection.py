from machine import Pin, SPI, Timer
import time, gc, math
try:
    import sensor, image, lcd  # K230 CanMV库
except ImportError:
    print("警告: 未找到CanMV库，使用模拟模式")

# MicroPython配置文件 - 统一管理所有变量
CONFIG = {
    'LCD_WIDTH': 320,
    'LCD_HEIGHT': 240,
    'SENSOR_WIDTH': 320,
    'SENSOR_HEIGHT': 240,
    'FPS': 20,  # MicroPython降低帧率
    'RECT_THICKNESS': 2,
    'MIN_AREA': 300,  # 最小检测区域
    'COLOR_THRESHOLD': 25,  # 颜色阈值
    'COLORS': {
        'red': [(30, 100, 15, 127, 15, 127), (255, 0, 0)],      # HSV阈值和RGB显示色
        'green': [(40, 80, -70, -10, -0, 30), (0, 255, 0)],
        'blue': [(0, 30, 0, 64, -128, 0), (0, 0, 255)],
        'yellow': [(20, 100, -10, 10, 20, 127), (255, 255, 0)]
    },
    'TIMER_PERIOD': 50  # 定时器周期(ms)
}

class MicroPythonColorDetector:
    def __init__(self):
        self.timer = None  # 定时器对象
        self.frame_count = 0
        self.last_time = time.ticks_ms()
        self.init_hardware()  # 初始化硬件

    def init_hardware(self):
        """初始化摄像头和LCD - MicroPython版本"""
        try:
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)
            sensor.skip_frames(time=2000)
            sensor.set_auto_gain(False)
            sensor.set_auto_whitebal(False)
            lcd.init()  # MicroPython版本不需要freq参数
            lcd.clear()
            print("硬件初始化成功")
        except Exception as e:
            print("硬件初始化失败:", e)

    def detect_color(self, img, color_name):
        """检测指定颜色并返回最大区域 - MicroPython优化版本"""
        threshold, display_color = CONFIG['COLORS'][color_name]
        try:
            blobs = img.find_blobs([threshold],
                                 pixels_threshold=CONFIG['MIN_AREA'],
                                 area_threshold=CONFIG['MIN_AREA'])

            if blobs:
                largest_blob = max(blobs, key=lambda b: b.pixels())  # 找到最大色块
                return largest_blob, display_color
        except Exception as e:
            print(f"颜色检测错误: {e}")
        return None, display_color

    def draw_rectangle(self, img, blob, color):
        """在检测到的色块上绘制矩形 - MicroPython版本"""
        try:
            img.draw_rectangle(blob.rect(), color=color, thickness=CONFIG['RECT_THICKNESS'])
            img.draw_cross(blob.cx(), blob.cy(), color=color, size=8, thickness=2)  # 绘制中心点
        except Exception as e:
            print(f"绘制错误: {e}")

    def display_info(self, img, color_name, blob):
        """显示检测信息 - MicroPython版本"""
        if blob:
            try:
                info_text = "{}: ({},{})".format(color_name, blob.cx(), blob.cy())  # MicroPython兼容格式
                img.draw_string(10, 10, info_text, color=(255, 255, 255), scale=1)
            except Exception as e:
                print(f"信息显示错误: {e}")
            
    def calculate_fps(self):
        """计算FPS - MicroPython版本"""
        current_time = time.ticks_ms()
        time_diff = time.ticks_diff(current_time, self.last_time)
        if time_diff > 1000:  # 每秒更新一次
            fps = self.frame_count * 1000 / time_diff
            self.frame_count = 0
            self.last_time = current_time
            return fps
        return 0

    def run(self):
        """主运行循环 - MicroPython版本"""
        print("开始颜色检测循环...")
        fps = 0

        while True:
            try:
                img = sensor.snapshot()
                self.frame_count += 1

                # 检测所有配置的颜色
                for color_name in CONFIG['COLORS'].keys():
                    blob, display_color = self.detect_color(img, color_name)
                    if blob:
                        self.draw_rectangle(img, blob, display_color)
                        self.display_info(img, color_name, blob)
                        break  # 只显示第一个检测到的颜色

                # 计算并显示FPS
                current_fps = self.calculate_fps()
                if current_fps > 0:
                    fps = current_fps

                fps_text = "FPS: {:.1f}".format(fps)  # MicroPython兼容格式
                img.draw_string(10, CONFIG['LCD_HEIGHT']-20, fps_text, color=(255, 255, 255), scale=1)

                lcd.display(img)
                gc.collect()  # 垃圾回收
                time.sleep_ms(CONFIG['TIMER_PERIOD'])  # 控制帧率

            except KeyboardInterrupt:
                print("用户中断")
                break
            except Exception as e:
                print("运行错误:", e)
                time.sleep_ms(100)

def main():
    """主函数 - MicroPython版本"""
    print("=== K230 MicroPython颜色检测器 ===")
    try:
        detector = MicroPythonColorDetector()
        print("颜色检测启动，支持颜色：", list(CONFIG['COLORS'].keys()))
        detector.run()
    except Exception as e:
        print("错误:", e)
    finally:
        try:
            lcd.clear()
        except:
            pass
        print("程序结束")

if __name__ == "__main__":
    main()
