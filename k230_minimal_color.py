# K230最简化颜色检测器 - 解决media_init问题
import time, gc

def test_k230_imports():
    """测试K230各种导入方式"""
    print("=== 测试K230导入方式 ===")
    
    # 方式1: 尝试官方camera模块
    try:
        from media.camera import *
        from media.display import *
        from media.media import *
        import image
        print("✅ 官方media模块导入成功")
        return 'official_media'
    except ImportError as e:
        print("❌ 官方media模块导入失败:", e)
    
    # 方式2: 尝试传统sensor模块
    try:
        import sensor, image, lcd
        print("✅ 传统sensor模块导入成功")
        return 'traditional_sensor'
    except ImportError as e:
        print("❌ 传统sensor模块导入失败:", e)
    
    # 方式3: 尝试machine模块
    try:
        from machine import Camera
        import image
        print("✅ machine.Camera导入成功")
        return 'machine_camera'
    except ImportError as e:
        print("❌ machine.Camera导入失败:", e)
    
    print("❌ 所有导入方式都失败")
    return None

def official_media_detector():
    """使用官方media模块的检测器"""
    print("启动官方media模块检测器...")
    
    try:
        from media.camera import *
        from media.display import *
        from media.media import *
        import image
        
        # 初始化显示
        display.init(LT9611_1920X1080_30FPS)
        print("✅ 显示初始化成功")
        
        # 初始化摄像头
        camera.sensor_init(CAM_DEV_ID_0, CAM_DEFAULT_SENSOR)
        print("✅ 摄像头初始化成功")
        
        # 设置输出参数
        out_width = ALIGN_UP(320, 16)
        out_height = 240
        camera.set_outsize(CAM_DEV_ID_0, CAM_CHN_ID_0, out_width, out_height)
        camera.set_outfmt(CAM_DEV_ID_0, CAM_CHN_ID_0, PIXEL_FORMAT_RGB_888)
        
        # 初始化媒体缓冲区
        ret = media.buffer_init()
        if ret:
            print("❌ 媒体缓冲区初始化失败")
            return False
        print("✅ 媒体缓冲区初始化成功")
        
        # 启动摄像头
        camera.start_stream(CAM_DEV_ID_0)
        print("✅ 摄像头启动成功")
        time.sleep(2)
        
        # 红色检测
        red_threshold = (30, 100, 15, 127, 15, 127)
        
        for i in range(20):  # 检测20帧
            try:
                img = camera.capture_image(CAM_DEV_ID_0, CAM_CHN_ID_0)
                
                if img != -1:
                    # 检测红色
                    blobs = img.find_blobs([red_threshold], pixels_threshold=100)
                    
                    if blobs:
                        largest = max(blobs, key=lambda b: b.pixels())
                        print(f"检测到红色: 位置({largest.cx()}, {largest.cy()}) 大小{largest.pixels()}")
                        
                        # 绘制检测结果
                        img.draw_rectangle(largest.rect(), color=(255, 0, 0), thickness=2)
                        img.draw_rectangle((250, 10, 40, 20), color=(255, 0, 0), thickness=-1, fill=True)
                    
                    img.draw_string(10, 10, f"帧 {i+1}/20", color=(255, 255, 255), scale=1)
                    
                    # 释放图像
                    camera.release_image(CAM_DEV_ID_0, CAM_CHN_ID_0, img)
                else:
                    print(f"第{i+1}帧捕获失败")
                    
            except Exception as e:
                print(f"第{i+1}帧处理出错:", str(e))
                
            time.sleep(0.2)
            gc.collect()
        
        print("✅ 官方media检测完成")
        return True
        
    except Exception as e:
        print("❌ 官方media检测出错:", str(e))
        return False
    finally:
        try:
            camera.stop_stream(CAM_DEV_ID_0)
            display.deinit()
            media.buffer_deinit()
            print("✅ 官方media资源清理完成")
        except:
            pass

def traditional_sensor_detector():
    """使用传统sensor模块的检测器"""
    print("启动传统sensor模块检测器...")
    
    try:
        import sensor, image, lcd
        
        # 初始化传感器
        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.QVGA)
        sensor.skip_frames(time=2000)
        print("✅ 传统sensor初始化成功")
        
        # 初始化LCD
        lcd.init()
        print("✅ LCD初始化成功")
        
        # 红色检测
        red_threshold = (30, 100, 15, 127, 15, 127)
        
        for i in range(20):  # 检测20帧
            try:
                img = sensor.snapshot()
                
                # 检测红色
                blobs = img.find_blobs([red_threshold], pixels_threshold=100)
                
                if blobs:
                    largest = max(blobs, key=lambda b: b.pixels())
                    print(f"检测到红色: 位置({largest.cx()}, {largest.cy()}) 大小{largest.pixels()}")
                    
                    # 绘制检测结果
                    img.draw_rectangle(largest.rect(), color=(255, 0, 0), thickness=2)
                    img.draw_rectangle((250, 10, 40, 20), color=(255, 0, 0), thickness=-1, fill=True)
                
                img.draw_string(10, 10, f"帧 {i+1}/20", color=(255, 255, 255), scale=1)
                
                # 显示图像
                lcd.display(img)
                
            except Exception as e:
                print(f"第{i+1}帧处理出错:", str(e))
                
            time.sleep(0.2)
            gc.collect()
        
        print("✅ 传统sensor检测完成")
        return True
        
    except Exception as e:
        print("❌ 传统sensor检测出错:", str(e))
        return False

def machine_camera_detector():
    """使用machine.Camera的检测器"""
    print("启动machine.Camera检测器...")
    
    try:
        from machine import Camera
        import image
        
        # 创建摄像头对象
        cam = Camera(0)  # 使用摄像头0
        print("✅ machine.Camera创建成功")
        
        # 红色检测
        red_threshold = (30, 100, 15, 127, 15, 127)
        
        for i in range(20):  # 检测20帧
            try:
                img = cam.snapshot()
                
                if img:
                    # 检测红色
                    blobs = img.find_blobs([red_threshold], pixels_threshold=100)
                    
                    if blobs:
                        largest = max(blobs, key=lambda b: b.pixels())
                        print(f"检测到红色: 位置({largest.cx()}, {largest.cy()}) 大小{largest.pixels()}")
                        
                        # 绘制检测结果
                        img.draw_rectangle(largest.rect(), color=(255, 0, 0), thickness=2)
                        img.draw_rectangle((250, 10, 40, 20), color=(255, 0, 0), thickness=-1, fill=True)
                    
                    img.draw_string(10, 10, f"帧 {i+1}/20", color=(255, 255, 255), scale=1)
                
            except Exception as e:
                print(f"第{i+1}帧处理出错:", str(e))
                
            time.sleep(0.2)
            gc.collect()
        
        print("✅ machine.Camera检测完成")
        return True
        
    except Exception as e:
        print("❌ machine.Camera检测出错:", str(e))
        return False

def create_compatibility_guide():
    """创建兼容性指南"""
    guide = """
# K230 CanMV兼容性指南

## 问题分析
`media_init` 函数未定义的问题说明K230使用的是官方camera模块，而不是传统的sensor模块。

## 解决方案

### 1. 使用官方camera模块 (推荐)
```python
from media.camera import *
from media.display import *
from media.media import *
import image

# 初始化流程
display.init(LT9611_1920X1080_30FPS)
camera.sensor_init(CAM_DEV_ID_0, CAM_DEFAULT_SENSOR)
media.buffer_init()
camera.start_stream(CAM_DEV_ID_0)
```

### 2. 使用传统sensor模块 (备用)
```python
import sensor, image, lcd

# 初始化流程
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
lcd.init()
```

### 3. 使用machine.Camera (实验性)
```python
from machine import Camera
import image

# 初始化流程
cam = Camera(0)
```

## 推荐使用文件
1. k230_official_color_detector.py - 官方API完整版
2. k230_minimal_color.py - 最简化测试版
3. fallback_detector.py - 备用方案

## 运行方法
```python
exec(open('k230_minimal_color.py').read())
```
"""
    
    try:
        with open('k230_compatibility_guide.md', 'w', encoding='utf-8') as f:
            f.write(guide)
        print("✅ 兼容性指南已创建: k230_compatibility_guide.md")
    except Exception as e:
        print("❌ 创建指南失败:", str(e))

def main():
    """主函数 - 自动检测并选择合适的方法"""
    print("🚀 K230最简化颜色检测器")
    print("自动检测最佳兼容方式...")
    
    # 测试导入方式
    import_type = test_k230_imports()
    
    if import_type == 'official_media':
        print("\\n使用官方media模块进行检测...")
        success = official_media_detector()
    elif import_type == 'traditional_sensor':
        print("\\n使用传统sensor模块进行检测...")
        success = traditional_sensor_detector()
    elif import_type == 'machine_camera':
        print("\\n使用machine.Camera进行检测...")
        success = machine_camera_detector()
    else:
        print("\\n❌ 无可用的摄像头模块")
        success = False
    
    # 创建兼容性指南
    create_compatibility_guide()
    
    if success:
        print("\\n🎉 检测完成！")
        print("推荐运行完整版本:")
        print("exec(open('k230_official_color_detector.py').read())")
    else:
        print("\\n💡 建议:")
        print("1. 检查K230固件版本")
        print("2. 确认摄像头连接")
        print("3. 查看兼容性指南: k230_compatibility_guide.md")

if __name__ == "__main__":
    main()
