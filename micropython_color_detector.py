from machine import Pin, Timer, freq
import time, gc, math
try:
    import sensor, image, lcd  # K230 CanMV库
    import ujson as json  # MicroPython JSON库
except ImportError:
    print("警告: 部分库未找到")

# MicroPython专用配置
MP_CONFIG = {
    'SYSTEM': {'cpu_freq': 400000000, 'mem_threshold': 50000},  # 系统配置
    'DISPLAY': {'width': 320, 'height': 240, 'refresh_ms': 50},
    'DETECTION': {'min_area': 200, 'max_colors': 3, 'sensitivity': 0.5},
    'PERFORMANCE': {'gc_interval': 10, 'fps_calc_frames': 15},
    'COLORS': {
        'red': {'hsv': (30, 100, 15, 127, 15, 127), 'rgb': (255, 0, 0), 'cn': '红'},
        'green': {'hsv': (40, 80, -70, -10, -0, 30), 'rgb': (0, 255, 0), 'cn': '绿'},
        'blue': {'hsv': (0, 30, 0, 64, -128, 0), 'rgb': (0, 0, 255), 'cn': '蓝'},
        'yellow': {'hsv': (20, 100, -10, 10, 20, 127), 'rgb': (255, 255, 0), 'cn': '黄'}
    }
}

class MicroPythonColorEngine:
    def __init__(self):
        self.objects = []  # 检测对象列表
        self.frame_cnt = 0
        self.fps = 0
        self.last_gc = 0
        self.start_time = time.ticks_ms()
        self.setup_hardware()
        
    def setup_hardware(self):
        """硬件设置优化"""
        try:
            # 设置CPU频率
            freq(MP_CONFIG['SYSTEM']['cpu_freq'])
            
            # 初始化传感器
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)
            sensor.skip_frames(time=1500)  # 减少等待时间
            sensor.set_auto_gain(False)
            sensor.set_auto_whitebal(False)
            
            # 初始化LCD
            lcd.init(freq=15000000)
            lcd.clear()
            print("MicroPython硬件初始化完成")
            
        except Exception as e:
            print("硬件初始化错误:", e)
            
    def memory_check(self):
        """内存检查和管理"""
        if gc.mem_free() < MP_CONFIG['SYSTEM']['mem_threshold']:
            gc.collect()
            print("内存清理:", gc.mem_free())
            
    def detect_colors_fast(self, img):
        """快速颜色检测算法"""
        self.objects.clear()
        
        for color_key, color_data in MP_CONFIG['COLORS'].items():
            try:
                blobs = img.find_blobs([color_data['hsv']], 
                                     pixels_threshold=MP_CONFIG['DETECTION']['min_area'])
                
                if blobs and len(self.objects) < MP_CONFIG['DETECTION']['max_colors']:
                    # 选择最大的色块
                    best_blob = max(blobs, key=lambda b: b.pixels())
                    if best_blob.density() > MP_CONFIG['DETECTION']['sensitivity']:
                        self.objects.append({
                            'blob': best_blob,
                            'color': color_data,
                            'name': color_key
                        })
                        
            except Exception as e:
                print("检测错误:", color_key, e)
                
    def draw_results_optimized(self, img):
        """优化的绘制结果"""
        for i, obj in enumerate(self.objects):
            blob = obj['blob']
            color_rgb = obj['color']['rgb']
            color_name = obj['color']['cn']
            
            try:
                # 绘制检测框
                img.draw_rectangle(blob.rect(), color=color_rgb, thickness=2)
                
                # 绘制中心点
                img.draw_cross(blob.cx(), blob.cy(), color=color_rgb, size=6)
                
                # 显示标签
                label = "{}:{}".format(color_name, blob.pixels())
                label_y = blob.y() - 12 if blob.y() > 15 else blob.y() + blob.h() + 2
                img.draw_string(blob.x(), label_y, label, color=color_rgb, scale=1)
                
            except Exception as e:
                print("绘制错误:", e)
                
    def generate_color_blocks(self, img):
        """生成颜色块显示"""
        block_w, block_h = 35, 25
        start_x = MP_CONFIG['DISPLAY']['width'] - block_w - 5
        
        for i, obj in enumerate(self.objects[:3]):  # 最多3个
            block_y = 5 + i * (block_h + 3)
            color_rgb = obj['color']['rgb']
            
            try:
                # 填充颜色块
                img.draw_rectangle((start_x, block_y, block_w, block_h), 
                                 color=color_rgb, thickness=-1, fill=True)
                # 白色边框
                img.draw_rectangle((start_x, block_y, block_w, block_h), 
                                 color=(255, 255, 255), thickness=1)
            except Exception as e:
                print("颜色块错误:", e)
                
    def update_fps(self):
        """更新FPS计算"""
        if self.frame_cnt % MP_CONFIG['PERFORMANCE']['fps_calc_frames'] == 0:
            current_time = time.ticks_ms()
            elapsed = time.ticks_diff(current_time, self.start_time)
            if elapsed > 0:
                self.fps = self.frame_cnt * 1000 / elapsed
                
    def draw_status(self, img):
        """绘制状态信息"""
        try:
            # FPS显示
            fps_text = "FPS:{:.1f}".format(self.fps)
            img.draw_string(5, 5, fps_text, color=(255, 255, 255), scale=1)
            
            # 对象计数
            count_text = "对象:{}".format(len(self.objects))
            img.draw_string(5, MP_CONFIG['DISPLAY']['height']-30, count_text, 
                           color=(255, 255, 255), scale=1)
            
            # 内存信息
            mem_text = "内存:{}K".format(gc.mem_free()//1024)
            img.draw_string(5, MP_CONFIG['DISPLAY']['height']-15, mem_text, 
                           color=(255, 255, 255), scale=1)
                           
        except Exception as e:
            print("状态显示错误:", e)
            
    def run_detection_loop(self):
        """主检测循环"""
        print("开始MicroPython颜色检测...")
        
        while True:
            try:
                # 获取图像
                img = sensor.snapshot()
                self.frame_cnt += 1
                
                # 执行检测
                self.detect_colors_fast(img)
                
                # 绘制结果
                self.draw_results_optimized(img)
                self.generate_color_blocks(img)
                
                # 更新状态
                self.update_fps()
                self.draw_status(img)
                
                # 显示图像
                lcd.display(img)
                
                # 内存管理
                if self.frame_cnt % MP_CONFIG['PERFORMANCE']['gc_interval'] == 0:
                    self.memory_check()
                    
                # 控制刷新率
                time.sleep_ms(MP_CONFIG['DISPLAY']['refresh_ms'])
                
            except KeyboardInterrupt:
                print("检测停止")
                break
            except Exception as e:
                print("循环错误:", e)
                time.sleep_ms(100)

def save_config():
    """保存配置到文件"""
    if json:
        try:
            with open('mp_config.json', 'w') as f:
                json.dump(MP_CONFIG, f)
            print("配置已保存")
        except:
            print("配置保存失败")

def main():
    """主程序"""
    print("=== K230 MicroPython颜色检测引擎 ===")
    print("支持颜色:", [MP_CONFIG['COLORS'][k]['cn'] for k in MP_CONFIG['COLORS']])
    
    save_config()
    
    try:
        engine = MicroPythonColorEngine()
        engine.run_detection_loop()
    except Exception as e:
        print("程序错误:", e)
    finally:
        try:
            lcd.clear()
        except:
            pass
        print("程序结束")

if __name__ == "__main__":
    main()
