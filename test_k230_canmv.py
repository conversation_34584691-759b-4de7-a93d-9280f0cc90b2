# K230 CanMV导入测试脚本
import time, gc

def test_media_imports():
    """测试K230 CanMV媒体模块导入"""
    print("=== K230 CanMV导入测试 ===")
    
    # 测试media.sensor导入
    try:
        from media.sensor import *
        print("✅ from media.sensor import * 成功")
    except ImportError as e:
        print("❌ media.sensor导入失败:", e)
        return False
        
    # 测试media.display导入
    try:
        from media.display import *
        print("✅ from media.display import * 成功")
    except ImportError as e:
        print("❌ media.display导入失败:", e)
        return False
        
    # 测试media.media导入
    try:
        from media.media import *
        print("✅ from media.media import * 成功")
    except ImportError as e:
        print("❌ media.media导入失败:", e)
        return False
        
    # 测试image模块
    try:
        import image
        print("✅ import image 成功")
    except ImportError as e:
        print("❌ image模块导入失败:", e)
        return False
        
    return True

def test_alternative_imports():
    """测试其他可能的导入方式"""
    print("\n=== 测试其他导入方式 ===")
    
    # 尝试直接导入sensor
    try:
        import sensor
        print("✅ import sensor 成功")
        return 'direct_sensor'
    except ImportError:
        print("❌ import sensor 失败")
        
    # 尝试从machine导入
    try:
        from machine import Camera
        print("✅ from machine import Camera 成功")
        return 'machine_camera'
    except ImportError:
        print("❌ from machine import Camera 失败")
        
    # 尝试从ulab导入
    try:
        import ulab
        print("✅ import ulab 成功")
        return 'ulab'
    except ImportError:
        print("❌ import ulab 失败")
        
    return None

def test_media_functions():
    """测试媒体功能"""
    print("\n=== 测试媒体功能 ===")
    
    try:
        from media.media import *
        from media.sensor import *
        from media.display import *
        
        # 测试媒体初始化
        media_init()
        print("✅ media_init() 成功")
        
        # 测试传感器创建
        sensor = Sensor(id=0)
        print("✅ Sensor(id=0) 创建成功")
        
        # 测试显示创建
        display = Display()
        print("✅ Display() 创建成功")
        
        # 清理
        media_deinit()
        print("✅ media_deinit() 成功")
        
        return True
        
    except Exception as e:
        print("❌ 媒体功能测试失败:", e)
        return False

def test_image_processing():
    """测试图像处理功能"""
    print("\n=== 测试图像处理功能 ===")
    
    try:
        import image
        
        # 创建测试图像
        img = image.Image(320, 240, image.RGB565)
        print("✅ 创建图像成功")
        
        # 测试绘制功能
        img.draw_rectangle((10, 10, 100, 80), color=(255, 0, 0), thickness=2)
        print("✅ 绘制矩形成功")
        
        img.draw_string(50, 50, "Test", color=(255, 255, 255))
        print("✅ 绘制文字成功")
        
        # 测试颜色检测功能
        red_threshold = (30, 100, 15, 127, 15, 127)
        blobs = img.find_blobs([red_threshold], pixels_threshold=100)
        print("✅ 颜色检测功能可用")
        
        return True
        
    except Exception as e:
        print("❌ 图像处理测试失败:", e)
        return False

def create_fallback_detector():
    """创建备用检测器"""
    print("\n=== 创建备用检测器 ===")
    
    fallback_code = '''
# K230备用颜色检测器
import time, gc

def fallback_color_detector():
    """备用颜色检测器"""
    print("启动备用模式...")
    
    # 尝试不同的导入方式
    sensor_module = None
    display_module = None
    
    # 方式1: CanMV标准导入
    try:
        from media.sensor import *
        from media.display import *
        from media.media import *
        import image
        
        media_init()
        sensor_module = Sensor(id=0)
        display_module = Display()
        print("✅ 使用CanMV标准模式")
        
    except:
        # 方式2: 直接导入
        try:
            import sensor, image, lcd
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)
            lcd.init()
            print("✅ 使用直接导入模式")
            
        except:
            print("❌ 所有导入方式都失败")
            return
    
    print("备用检测器准备就绪")

if __name__ == "__main__":
    fallback_color_detector()
'''
    
    try:
        with open('fallback_detector.py', 'w') as f:
            f.write(fallback_code)
        print("✅ 备用检测器已创建: fallback_detector.py")
        return True
    except Exception as e:
        print("❌ 创建备用检测器失败:", e)
        return False

def generate_compatibility_report():
    """生成兼容性报告"""
    print("\n=== 生成兼容性报告 ===")
    
    report = []
    report.append("# K230 CanMV兼容性报告")
    report.append(f"测试时间: {time.localtime()}")
    report.append("")
    
    # 测试导入
    media_ok = test_media_imports()
    report.append(f"媒体模块导入: {'✅ 成功' if media_ok else '❌ 失败'}")
    
    # 测试其他导入方式
    alt_import = test_alternative_imports()
    report.append(f"备用导入方式: {alt_import if alt_import else '❌ 无可用方式'}")
    
    # 测试功能
    if media_ok:
        func_ok = test_media_functions()
        report.append(f"媒体功能测试: {'✅ 成功' if func_ok else '❌ 失败'}")
        
        img_ok = test_image_processing()
        report.append(f"图像处理测试: {'✅ 成功' if img_ok else '❌ 失败'}")
    else:
        report.append("媒体功能测试: ⏭️ 跳过（导入失败）")
        report.append("图像处理测试: ⏭️ 跳过（导入失败）")
    
    # 推荐使用的文件
    report.append("")
    report.append("## 推荐使用的文件:")
    if media_ok:
        report.append("- k230_canmv_color_detector.py (完整版)")
        report.append("- simple_k230_color.py (简化版)")
    else:
        report.append("- fallback_detector.py (备用版)")
        
    # 保存报告
    try:
        with open('compatibility_report.txt', 'w') as f:
            f.write('\\n'.join(report))
        print("✅ 兼容性报告已保存: compatibility_report.txt")
    except:
        print("❌ 保存报告失败")
        
    # 打印报告
    print("\\n" + "\\n".join(report))

def main():
    """主测试函数"""
    print("🚀 开始K230 CanMV兼容性测试")
    
    try:
        # 基本导入测试
        media_ok = test_media_imports()
        
        # 如果基本导入失败，测试其他方式
        if not media_ok:
            alt_import = test_alternative_imports()
            if not alt_import:
                print("\\n❌ 所有导入方式都失败")
                print("请检查:")
                print("1. K230固件版本")
                print("2. CanMV环境配置")
                print("3. 文件路径和权限")
                return
        
        # 功能测试
        if media_ok:
            test_media_functions()
            test_image_processing()
        
        # 创建备用方案
        create_fallback_detector()
        
        # 生成报告
        generate_compatibility_report()
        
        print("\\n🎉 测试完成！")
        
        if media_ok:
            print("推荐运行: exec(open('simple_k230_color.py').read())")
        else:
            print("推荐运行: exec(open('fallback_detector.py').read())")
            
    except Exception as e:
        print("\\n💥 测试程序异常:", e)
    finally:
        gc.collect()
        print("\\n✅ 测试程序结束")

if __name__ == "__main__":
    main()
