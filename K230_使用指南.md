# K230颜色检测使用指南

## 🚨 遇到任何错误？

按以下步骤逐一解决：

### 第一步：运行最简测试（诊断问题）
```python
exec(open('k230_minimal_test.py').read())
```

### 第二步：运行最终修复版（推荐）
```python
exec(open('k230_final_color_detector.py').read())
```

### 第三步：如果还有传感器冲突
```python
exec(open('k230_sensor_reset.py').read())
```

## 🎯 正常使用流程

### 1. 首次使用 - 推荐运行测试
```python
# 最简测试工具 - 诊断问题
exec(open('k230_minimal_test.py').read())
```

### 2. 正常使用 - 完整功能
```python
# 最终修复版 - 推荐使用
exec(open('k230_final_color_detector.py').read())
```

## 📁 文件说明

| 文件名 | 功能 | 推荐度 | 说明 |
|--------|------|--------|------|
| `k230_final_color_detector.py` | 最终修复版检测器 | ⭐⭐⭐⭐ | 解决所有已知问题，推荐使用 |
| `k230_minimal_test.py` | 最简测试工具 | ⭐⭐⭐ | 诊断问题，快速测试 |
| `k230_sensor_reset.py` | 传感器重置工具 | ⭐⭐⭐ | 解决sensor冲突问题 |
| `k230_correct_color_detector.py` | 标准版检测器 | ⭐⭐ | 基于官方文档 |
| `k230_fixed_color_detector.py` | 修复版检测器 | ⭐⭐ | 自动处理传感器冲突 |

## 🔧 常见问题快速解决

### 问题1: import * not at module level
```python
# 已修复！运行最终版本
exec(open('k230_final_color_detector.py').read())
```

### 问题2: sensor already inited
```python
exec(open('k230_sensor_reset.py').read())
```

### 问题3: 没有图像显示
1. 检查摄像头连接
2. 重新连接CanMV IDE
3. 运行测试：`exec(open('k230_minimal_test.py').read())`

### 问题4: 检测不到颜色
1. 使用红色、绿色、蓝色纯色物体测试
2. 确保光照充足
3. 物体要足够大（建议手掌大小）

### 问题5: 程序运行缓慢
1. 降低分辨率（已在代码中优化）
2. 减少检测颜色数量
3. 增加检测间隔时间

### 问题6: 所有方法都试过了
1. 重启K230开发板
2. 重新连接CanMV IDE
3. 运行：`exec(open('k230_final_color_detector.py').read())`

## 🎨 颜色检测说明

### 支持的颜色
- 🔴 红色 (Red)
- 🟢 绿色 (Green) 
- 🔵 蓝色 (Blue)

### 检测效果
- 实时显示检测框
- 显示颜色坐标
- 生成对应颜色的矩形块
- 显示FPS和检测统计

### 最佳使用条件
1. **光照**: 自然光或白色LED灯
2. **背景**: 白色或浅色背景
3. **物体**: 纯色、较大的物体
4. **距离**: 摄像头前20-50cm

## 📞 技术支持

如果遇到其他问题：

1. **重启设备**: 重启K230开发板
2. **重新连接**: 重新连接CanMV IDE
3. **检查硬件**: 确认摄像头连接正常
4. **运行诊断**: `exec(open('k230_minimal_test.py').read())`
5. **运行最终版**: `exec(open('k230_final_color_detector.py').read())`

## 🚀 进阶使用

### 修改颜色阈值
在代码中找到颜色配置部分：
```python
'colors': {
    'red': [(30, 100, 15, 127, 15, 127), (255, 0, 0), '红色'],
    'green': [(40, 80, -70, -10, -0, 30), (0, 255, 0), '绿色'],
    'blue': [(0, 30, 0, 64, -128, 0), (0, 0, 255), '蓝色']
}
```

### 添加新颜色
1. 使用颜色阈值工具确定LAB值
2. 在colors字典中添加新条目
3. 重新运行程序

### 调整检测参数
```python
'detect': {
    'min_area': 200,      # 最小检测区域
    'max_objects': 2      # 每种颜色最多检测数量
}
```

## ✅ 成功运行的标志

当程序正常运行时，你会看到：
- ✅ 摄像头图像正常显示
- ✅ 检测框出现在彩色物体上
- ✅ 右侧显示对应颜色的矩形块
- ✅ 左上角显示FPS和检测统计
- ✅ 控制台输出检测信息

祝你使用愉快！🎉
