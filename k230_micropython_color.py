# K230 MicroPython专用颜色检测器
import sensor, image, time, lcd
from machine import Pin
import gc

# 配置参数 - 统一管理
CONFIG = {
    'hw': {'w': 320, 'h': 240, 'fps': 20},  # 硬件参数
    'detect': {'min_area': 100, 'max_count': 2},  # 检测参数
    'visual': {'thick': 2, 'rect_w': 40, 'rect_h': 25},  # 视觉参数
    'colors': {  # 颜色定义
        'red': [(30, 100, 15, 127, 15, 127), (255, 0, 0), '红'],
        'green': [(40, 80, -70, -10, -0, 30), (0, 255, 0), '绿'],
        'blue': [(0, 30, 0, 64, -128, 0), (0, 0, 255), '蓝'],
        'yellow': [(20, 100, -10, 10, 20, 127), (255, 255, 0), '黄']
    }
}

class MPColorDetector:
    def __init__(self):
        self.fps_count = 0  # FPS计数
        self.last_fps_time = time.ticks_ms()  # 上次FPS时间
        self.current_fps = 0  # 当前FPS
        self.detected = []  # 检测结果
        self.init_hw()  # 初始化硬件
        
    def init_hw(self):
        """硬件初始化"""
        try:
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)
            sensor.skip_frames(time=2000)
            sensor.set_auto_gain(False)
            sensor.set_auto_whitebal(False)
            lcd.init()
            lcd.clear()
            print("硬件初始化OK")
        except Exception as e:
            print("硬件错误:", e)
            
    def detect_colors(self, img):
        """检测所有颜色"""
        self.detected.clear()
        for name, (hsv, rgb, cn) in CONFIG['colors'].items():
            try:
                blobs = img.find_blobs([hsv], pixels_threshold=CONFIG['detect']['min_area'])
                for blob in blobs[:CONFIG['detect']['max_count']]:
                    self.detected.append({
                        'blob': blob, 'rgb': rgb, 'name': name, 'cn': cn
                    })
            except:
                continue
                
    def draw_detections(self, img):
        """绘制检测结果"""
        for item in self.detected:
            blob, rgb = item['blob'], item['rgb']
            try:
                # 绘制矩形框
                img.draw_rectangle(blob.rect(), color=rgb, thickness=CONFIG['visual']['thick'])
                # 绘制中心十字
                img.draw_cross(blob.cx(), blob.cy(), color=rgb, size=5)
                # 显示标签
                label = item['cn'] + ":" + str(blob.pixels())
                img.draw_string(blob.x(), blob.y()-12, label, color=rgb)
            except:
                continue
                
    def generate_color_blocks(self, img):
        """生成颜色矩形块"""
        block_w = CONFIG['visual']['rect_w']
        block_h = CONFIG['visual']['rect_h']
        start_x = CONFIG['hw']['w'] - block_w - 5
        
        for i, item in enumerate(self.detected[:3]):  # 最多3个
            y = 10 + i * (block_h + 5)
            rgb = item['rgb']
            try:
                # 填充颜色块
                img.draw_rectangle((start_x, y, block_w, block_h), 
                                 color=rgb, thickness=-1, fill=True)
                # 白色边框
                img.draw_rectangle((start_x, y, block_w, block_h), 
                                 color=(255, 255, 255), thickness=1)
                # 颜色名称
                img.draw_string(start_x+2, y+8, item['cn'], color=(255, 255, 255))
            except:
                continue
                
    def calc_fps(self):
        """计算FPS"""
        self.fps_count += 1
        now = time.ticks_ms()
        if time.ticks_diff(now, self.last_fps_time) >= 1000:
            self.current_fps = self.fps_count
            self.fps_count = 0
            self.last_fps_time = now
            
    def draw_info(self, img):
        """绘制信息"""
        try:
            # FPS
            fps_text = "FPS:" + str(self.current_fps)
            img.draw_string(5, 5, fps_text, color=(255, 255, 255))
            # 检测数量
            count_text = "检测:" + str(len(self.detected))
            img.draw_string(5, CONFIG['hw']['h']-20, count_text, color=(255, 255, 255))
        except:
            pass
            
    def run(self):
        """主循环"""
        print("开始检测，支持颜色:", [cn for _, _, cn in CONFIG['colors'].values()])
        
        while True:
            try:
                img = sensor.snapshot()  # 获取图像
                
                self.detect_colors(img)  # 检测颜色
                self.draw_detections(img)  # 绘制检测
                self.generate_color_blocks(img)  # 生成颜色块
                self.calc_fps()  # 计算FPS
                self.draw_info(img)  # 绘制信息
                
                lcd.display(img)  # 显示
                gc.collect()  # 垃圾回收
                time.sleep_ms(30)  # 控制帧率
                
            except KeyboardInterrupt:
                print("停止检测")
                break
            except Exception as e:
                print("运行错误:", e)
                time.sleep_ms(100)

def simple_red_detector():
    """简单红色检测器"""
    print("启动简单红色检测器...")
    
    # 初始化
    sensor.reset()
    sensor.set_pixformat(sensor.RGB565)
    sensor.set_framesize(sensor.QVGA)
    sensor.skip_frames(time=2000)
    lcd.init()
    lcd.clear()
    
    red_hsv = (30, 100, 15, 127, 15, 127)  # 红色阈值
    
    while True:
        try:
            img = sensor.snapshot()
            blobs = img.find_blobs([red_hsv], pixels_threshold=100)
            
            if blobs:
                # 找最大色块
                largest = max(blobs, key=lambda b: b.pixels())
                
                # 绘制检测框
                img.draw_rectangle(largest.rect(), color=(255, 0, 0), thickness=2)
                
                # 生成红色矩形块
                img.draw_rectangle((270, 10, 40, 25), color=(255, 0, 0), 
                                 thickness=-1, fill=True)
                img.draw_rectangle((270, 10, 40, 25), color=(255, 255, 255), thickness=1)
                
                # 显示信息
                info = "红色:(" + str(largest.cx()) + "," + str(largest.cy()) + ")"
                img.draw_string(10, 10, info, color=(255, 255, 255))
            
            img.draw_string(10, 220, "简单红色检测", color=(255, 255, 255))
            lcd.display(img)
            gc.collect()
            time.sleep_ms(50)
            
        except KeyboardInterrupt:
            break
        except:
            time.sleep_ms(100)

def test_hardware():
    """硬件测试"""
    try:
        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.QVGA)
        lcd.init()
        lcd.clear()
        
        # 显示测试图像
        for i in range(10):
            img = sensor.snapshot()
            img.draw_string(100, 100, "硬件测试OK", color=(255, 255, 255))
            lcd.display(img)
            time.sleep_ms(500)
            
        return True
    except Exception as e:
        print("硬件测试失败:", e)
        return False

def main():
    """主函数"""
    print("=== K230 MicroPython颜色检测器 ===")
    
    # 硬件测试
    if not test_hardware():
        print("硬件测试失败，程序退出")
        return
        
    try:
        # 启动完整检测器
        detector = MPColorDetector()
        detector.run()
    except Exception as e:
        print("完整检测器错误:", e)
        print("切换到简单模式...")
        simple_red_detector()
    finally:
        try:
            lcd.clear()
        except:
            pass
        print("程序结束")

if __name__ == "__main__":
    main()
