# K230 MicroPython测试脚本
import time
import gc

def test_imports():
    """测试库导入"""
    print("=== 库导入测试 ===")
    
    try:
        import sensor
        print("✅ sensor库导入成功")
    except ImportError as e:
        print("❌ sensor库导入失败:", e)
        return False
        
    try:
        import image
        print("✅ image库导入成功")
    except ImportError as e:
        print("❌ image库导入失败:", e)
        return False
        
    try:
        import lcd
        print("✅ lcd库导入成功")
    except ImportError as e:
        print("❌ lcd库导入失败:", e)
        return False
        
    try:
        from machine import Pin
        print("✅ machine库导入成功")
    except ImportError as e:
        print("❌ machine库导入失败:", e)
        return False
        
    return True

def test_lcd_init():
    """测试LCD初始化"""
    print("\n=== LCD初始化测试 ===")
    
    try:
        import lcd
        # 测试不带参数的初始化
        lcd.init()
        print("✅ lcd.init() 成功")
        lcd.clear()
        print("✅ lcd.clear() 成功")
        return True
    except Exception as e:
        print("❌ LCD初始化失败:", e)
        return False

def test_sensor_init():
    """测试传感器初始化"""
    print("\n=== 传感器初始化测试 ===")
    
    try:
        import sensor
        sensor.reset()
        print("✅ sensor.reset() 成功")
        
        sensor.set_pixformat(sensor.RGB565)
        print("✅ sensor.set_pixformat() 成功")
        
        sensor.set_framesize(sensor.QVGA)
        print("✅ sensor.set_framesize() 成功")
        
        sensor.skip_frames(time=1000)
        print("✅ sensor.skip_frames() 成功")
        
        return True
    except Exception as e:
        print("❌ 传感器初始化失败:", e)
        return False

def test_image_capture():
    """测试图像捕获"""
    print("\n=== 图像捕获测试 ===")
    
    try:
        import sensor, image, lcd
        
        # 初始化
        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.QVGA)
        sensor.skip_frames(time=1000)
        lcd.init()
        lcd.clear()
        
        # 捕获图像
        img = sensor.snapshot()
        print("✅ 图像捕获成功，尺寸:", img.width(), "x", img.height())
        
        # 绘制测试
        img.draw_string(10, 10, "MicroPython Test", color=(255, 255, 255))
        img.draw_rectangle((50, 50, 100, 80), color=(255, 0, 0), thickness=2)
        print("✅ 图像绘制成功")
        
        # 显示测试
        lcd.display(img)
        print("✅ 图像显示成功")
        
        return True
    except Exception as e:
        print("❌ 图像捕获失败:", e)
        return False

def test_color_detection():
    """测试颜色检测"""
    print("\n=== 颜色检测测试 ===")
    
    try:
        import sensor, image, lcd
        
        # 初始化
        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.QVGA)
        sensor.skip_frames(time=1000)
        lcd.init()
        lcd.clear()
        
        # 红色阈值
        red_threshold = (30, 100, 15, 127, 15, 127)
        
        # 测试检测
        for i in range(5):
            img = sensor.snapshot()
            blobs = img.find_blobs([red_threshold], pixels_threshold=100)
            
            if blobs:
                print("✅ 检测到", len(blobs), "个红色区域")
                for blob in blobs:
                    img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
            else:
                print("⚪ 未检测到红色区域")
                
            img.draw_string(10, 10, "Color Test " + str(i+1), color=(255, 255, 255))
            lcd.display(img)
            time.sleep_ms(500)
            
        return True
    except Exception as e:
        print("❌ 颜色检测失败:", e)
        return False

def test_memory():
    """测试内存管理"""
    print("\n=== 内存管理测试 ===")
    
    try:
        # 内存信息
        gc.collect()
        mem_free = gc.mem_free()
        mem_alloc = gc.mem_alloc()
        
        print("✅ 可用内存:", mem_free, "字节")
        print("✅ 已用内存:", mem_alloc, "字节")
        print("✅ 总内存:", mem_free + mem_alloc, "字节")
        
        # 垃圾回收测试
        test_list = [i for i in range(1000)]
        del test_list
        gc.collect()
        
        new_mem_free = gc.mem_free()
        print("✅ 垃圾回收后可用内存:", new_mem_free, "字节")
        
        return True
    except Exception as e:
        print("❌ 内存测试失败:", e)
        return False

def test_performance():
    """测试性能"""
    print("\n=== 性能测试 ===")
    
    try:
        import sensor, image, lcd
        
        # 初始化
        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.QVGA)
        sensor.skip_frames(time=1000)
        lcd.init()
        lcd.clear()
        
        # FPS测试
        frame_count = 0
        start_time = time.ticks_ms()
        
        for i in range(30):  # 测试30帧
            img = sensor.snapshot()
            img.draw_string(10, 10, "FPS Test", color=(255, 255, 255))
            lcd.display(img)
            frame_count += 1
            gc.collect()
            
        end_time = time.ticks_ms()
        duration = time.ticks_diff(end_time, start_time)
        fps = frame_count * 1000 / duration
        
        print("✅ 平均FPS:", round(fps, 2))
        print("✅ 总耗时:", duration, "毫秒")
        
        return True
    except Exception as e:
        print("❌ 性能测试失败:", e)
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始K230 MicroPython兼容性测试\n")
    
    tests = [
        ("库导入", test_imports),
        ("LCD初始化", test_lcd_init),
        ("传感器初始化", test_sensor_init),
        ("图像捕获", test_image_capture),
        ("颜色检测", test_color_detection),
        ("内存管理", test_memory),
        ("性能测试", test_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n📋 正在测试: {name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {name} 测试通过")
            else:
                print(f"❌ {name} 测试失败")
        except Exception as e:
            print(f"❌ {name} 测试异常:", e)
            
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MicroPython环境正常")
        return True
    else:
        print("⚠️  部分测试失败，请检查硬件和环境配置")
        return False

def main():
    """主函数"""
    try:
        success = run_all_tests()
        
        if success:
            print("\n🚀 可以安全运行颜色检测程序:")
            print("   exec(open('k230_micropython_color.py').read())")
        else:
            print("\n⚠️  建议先解决测试失败的问题")
            
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print("\n💥 测试程序异常:", e)
    finally:
        try:
            import lcd
            lcd.clear()
        except:
            pass
        print("\n✅ 测试程序结束")

if __name__ == "__main__":
    main()
