# 庐山派K230紧急修复 - 最简单的解决方案
import time, gc

print("🚨 庐山派K230紧急修复工具")
print("解决传感器初始化失败问题")

# 导入模块
try:
    from media.sensor import *
    from media.display import *
    from media.media import *
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("请检查:")
    print("1. 庐山派K230固件版本")
    print("2. CanMV IDE连接")
    print("3. 重启开发板")
    exit()

def emergency_cleanup():
    """紧急清理"""
    print("🔧 紧急清理资源...")
    try:
        # 强制停止所有传感器
        for i in range(5):
            try:
                s = Sensor(id=i)
                s.stop()
            except:
                pass
        
        # 清理系统
        try: MediaManager.deinit()
        except: pass
        try: Display.deinit()
        except: pass
        
        gc.collect()
        time.sleep(2)
        print("✅ 清理完成")
        return True
    except Exception as e:
        print(f"清理出错: {e}")
        return False

def find_working_sensor():
    """寻找可用传感器"""
    print("🔍 寻找可用传感器...")
    
    # 尝试更多传感器ID
    sensor_ids = [0, 1, 2, 3, 4, 5]
    
    for sensor_id in sensor_ids:
        try:
            print(f"尝试传感器ID: {sensor_id}")
            sensor = Sensor(id=sensor_id)
            sensor.reset()
            print(f"✅ 传感器ID {sensor_id} 可用")
            return sensor, sensor_id
        except Exception as e:
            print(f"传感器ID {sensor_id}: {str(e)}")
            continue
    
    print("❌ 没有找到可用传感器")
    return None, None

def minimal_test():
    """最小化测试"""
    print("🧪 最小化测试...")
    
    # 清理
    emergency_cleanup()
    
    # 寻找传感器
    sensor, sensor_id = find_working_sensor()
    if not sensor:
        print("❌ 传感器测试失败")
        print("\\n🔧 建议解决方案:")
        print("1. 重启庐山派K230开发板")
        print("2. 重新连接CanMV IDE")
        print("3. 检查摄像头连接线")
        print("4. 确认电源供电充足")
        print("5. 检查固件版本兼容性")
        return False
    
    try:
        # 最小配置
        sensor.set_framesize(Sensor.QQVGA)  # 160x120 最小分辨率
        sensor.set_pixformat(Sensor.RGB565)
        
        # 初始化显示
        Display.init(Display.VIRT, 160, 120)
        
        # 初始化媒体
        MediaManager.init()
        
        # 启动
        sensor.run()
        time.sleep(2)
        
        print(f"✅ 传感器ID {sensor_id} 初始化成功")
        
        # 测试捕获
        for i in range(3):
            img = sensor.snapshot()
            if img:
                print(f"✅ 第{i+1}次图像捕获成功: {img.width()}x{img.height()}")
                
                # 简单绘制
                img.draw_string(5, 5, f"庐山派{i+1}", color=(255, 255, 255), scale=1)
                img.draw_rectangle((10, 20, 50, 30), color=(0, 255, 0), thickness=2)
                
                Display.show_image(img)
            else:
                print(f"❌ 第{i+1}次图像捕获失败")
            
            time.sleep(1)
        
        print("\\n🎉 庐山派K230修复成功！")
        print("现在可以运行其他程序了")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def quick_color_test():
    """快速颜色测试"""
    print("🎨 快速颜色测试...")
    
    # 清理并初始化
    emergency_cleanup()
    sensor, sensor_id = find_working_sensor()
    
    if not sensor:
        print("❌ 传感器不可用")
        return False
    
    try:
        # 配置
        sensor.set_framesize(Sensor.QVGA)  # 320x240
        sensor.set_pixformat(Sensor.RGB565)
        Display.init(Display.VIRT, 320, 240)
        MediaManager.init()
        sensor.run()
        time.sleep(2)
        
        # 红色检测
        red_threshold = (30, 100, 15, 127, 15, 127)
        
        print("请在摄像头前放置红色物体...")
        
        for i in range(10):
            img = sensor.snapshot()
            if img:
                # 检测红色
                blobs = img.find_blobs([red_threshold], pixels_threshold=100)
                
                # 绘制
                for blob in blobs:
                    img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
                    img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 0), size=5, thickness=2)
                
                # 信息
                img.draw_string(5, 5, f"庐山派红色测试 {i+1}/10", color=(255, 255, 255), scale=1)
                img.draw_string(5, 20, f"检测到: {len(blobs)}个", color=(255, 255, 255), scale=1)
                
                if blobs:
                    img.draw_string(5, 35, "发现红色!", color=(255, 0, 0), scale=1)
                    print(f"第{i+1}次: 检测到{len(blobs)}个红色对象")
                
                Display.show_image(img)
            
            time.sleep(0.5)
        
        print("✅ 颜色测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 颜色测试失败: {str(e)}")
        return False

def show_solutions():
    """显示解决方案"""
    print("""
🏔️ 庐山派K230传感器问题解决方案

❌ 如果看到"所有传感器ID初始化失败":

🔧 立即尝试:
1. 重启庐山派K230开发板 (最重要!)
2. 重新连接CanMV IDE
3. 运行: exec(open('lushan_emergency_fix.py').read())

🔍 检查项目:
1. 摄像头连接线是否松动
2. 电源供电是否充足
3. CanMV IDE版本是否兼容
4. 固件版本是否正确

⚡ 快速修复命令:
# 紧急修复
emergency_cleanup()
minimal_test()

# 如果修复成功，运行颜色测试
quick_color_test()

🎯 修复成功后可以运行:
exec(open('lushan_simple_color.py').read())
""")

def main():
    """主函数"""
    print("🏔️ 庐山派K230紧急修复工具")
    
    show_solutions()
    
    print("\\n开始紧急修复...")
    
    if minimal_test():
        print("\\n✅ 基本功能修复成功!")
        
        user_input = input("\\n是否进行颜色测试? (y/n): ")
        if user_input.lower() == 'y':
            quick_color_test()
        
        print("\\n🎉 修复完成!")
        print("现在可以运行完整程序:")
        print("exec(open('lushan_simple_color.py').read())")
    else:
        print("\\n❌ 修复失败")
        print("请尝试:")
        print("1. 重启庐山派K230开发板")
        print("2. 检查硬件连接")
        print("3. 重新连接CanMV IDE")

if __name__ == "__main__":
    main()
