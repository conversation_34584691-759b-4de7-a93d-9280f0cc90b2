# K230传感器重置工具 - 解决sensor already inited问题
import time, gc, sys

def force_cleanup():
    """强制清理所有传感器资源"""
    print("🔧 开始强制清理传感器资源...")
    
    try:
        # 导入模块
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        print("✅ 模块导入成功")
        
        # 1. 尝试停止所有可能的传感器
        sensor_ids = [0, 1, 2]
        for sensor_id in sensor_ids:
            try:
                # 尝试创建并立即停止
                temp_sensor = Sensor(id=sensor_id)
                temp_sensor.stop()
                print(f"✅ 停止传感器ID {sensor_id}")
            except Exception as e:
                print(f"传感器ID {sensor_id}: {str(e)}")
        
        # 2. 清理媒体管理器
        try:
            MediaManager.deinit()
            print("✅ 媒体管理器已清理")
        except Exception as e:
            print(f"媒体管理器清理: {str(e)}")
        
        # 3. 清理显示
        try:
            Display.deinit()
            print("✅ 显示已清理")
        except Exception as e:
            print(f"显示清理: {str(e)}")
        
        # 4. 清理全局变量
        globals_to_clear = ['sensor', 'display', 'media']
        for var_name in globals_to_clear:
            if var_name in globals():
                del globals()[var_name]
                print(f"✅ 清理全局变量: {var_name}")
        
        # 5. 垃圾回收
        gc.collect()
        print("✅ 垃圾回收完成")
        
        # 6. 等待系统稳定
        time.sleep(2)
        print("✅ 系统稳定等待完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 强制清理失败: {str(e)}")
        return False

def test_sensor_availability():
    """测试传感器可用性"""
    print("\\n🔍 测试传感器可用性...")
    
    try:
        from media.sensor import *
        
        sensor_ids = [2, 0, 1]  # 按优先级测试
        available_sensors = []
        
        for sensor_id in sensor_ids:
            try:
                print(f"测试传感器ID {sensor_id}...")
                sensor = Sensor(id=sensor_id)
                sensor.reset()
                
                # 如果能到这里说明传感器可用
                available_sensors.append(sensor_id)
                print(f"✅ 传感器ID {sensor_id} 可用")
                
                # 立即停止以释放资源
                sensor.stop()
                del sensor
                gc.collect()
                
            except Exception as e:
                print(f"❌ 传感器ID {sensor_id} 不可用: {str(e)}")
        
        if available_sensors:
            print(f"\\n✅ 可用的传感器ID: {available_sensors}")
            return available_sensors[0]  # 返回第一个可用的
        else:
            print("\\n❌ 没有可用的传感器")
            return None
            
    except Exception as e:
        print(f"❌ 传感器可用性测试失败: {str(e)}")
        return None

def safe_sensor_test():
    """安全的传感器测试"""
    print("\\n🧪 开始安全传感器测试...")
    
    # 强制清理
    if not force_cleanup():
        print("❌ 清理失败，无法继续")
        return False
    
    # 测试可用性
    available_id = test_sensor_availability()
    if available_id is None:
        print("❌ 没有可用传感器")
        return False
    
    try:
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        print(f"\\n使用传感器ID: {available_id}")
        
        # 创建传感器
        sensor = Sensor(id=available_id)
        sensor.reset()
        sensor.set_framesize(Sensor.QQVGA)  # 最小分辨率160x120
        sensor.set_pixformat(Sensor.RGB565)
        
        print("✅ 传感器配置完成")
        
        # 初始化显示
        Display.init(Display.VIRT, sensor.width(), sensor.height())
        print("✅ 显示初始化完成")
        
        # 初始化媒体管理器
        MediaManager.init()
        print("✅ 媒体管理器初始化完成")
        
        # 启动传感器
        sensor.run()
        print("✅ 传感器启动完成")
        
        # 等待稳定
        time.sleep(2)
        
        # 测试图像捕获
        print("\\n📸 测试图像捕获...")
        for i in range(3):
            try:
                img = sensor.snapshot()
                if img:
                    print(f"✅ 第{i+1}次捕获成功: {img.width()}x{img.height()}")
                    
                    # 简单绘制
                    img.draw_string(10, 10, f"测试 {i+1}/3", color=(255, 255, 255), scale=1)
                    img.draw_rectangle((20, 20, 40, 30), color=(0, 255, 0), thickness=2)
                    
                    # 显示
                    Display.show_image(img)
                else:
                    print(f"❌ 第{i+1}次捕获失败")
                    
            except Exception as e:
                print(f"❌ 第{i+1}次捕获异常: {str(e)}")
                
            time.sleep(0.5)
        
        print("\\n✅ 传感器测试完成")
        print("现在可以运行其他颜色检测程序了")
        
        # 保持传感器运行，不要停止
        return True
        
    except Exception as e:
        print(f"❌ 安全传感器测试失败: {str(e)}")
        return False

def quick_reset():
    """快速重置"""
    print("⚡ 快速重置传感器...")
    
    try:
        # 简单的重置流程
        force_cleanup()
        time.sleep(1)
        
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        # 尝试最简单的初始化
        sensor = Sensor()  # 使用默认参数
        sensor.reset()
        
        print("✅ 快速重置成功")
        print("可以尝试运行颜色检测程序")
        return True
        
    except Exception as e:
        print(f"❌ 快速重置失败: {str(e)}")
        return False

def show_help():
    """显示帮助信息"""
    print("""
🆘 K230传感器问题解决方案

当遇到 'sensor is already inited' 错误时：

1. 运行此重置工具:
   exec(open('k230_sensor_reset.py').read())

2. 然后运行修复版检测器:
   exec(open('k230_fixed_color_detector.py').read())

3. 如果还有问题，尝试:
   - 重启K230开发板
   - 重新连接CanMV IDE
   - 检查摄像头连接

4. 其他可用程序:
   - k230_simple_test.py (基础测试)
   - k230_correct_color_detector.py (标准版本)
""")

def main():
    """主函数"""
    print("🔧 K230传感器重置工具")
    print("解决 'sensor is already inited' 问题")
    
    print("\\n选择操作:")
    print("1. 强制清理传感器")
    print("2. 安全传感器测试")
    print("3. 快速重置")
    print("4. 显示帮助")
    
    # 自动执行安全测试
    print("\\n自动执行安全传感器测试...")
    
    if safe_sensor_test():
        print("\\n🎉 传感器重置成功！")
        print("\\n现在可以运行:")
        print("exec(open('k230_fixed_color_detector.py').read())")
    else:
        print("\\n❌ 传感器重置失败")
        print("\\n尝试快速重置...")
        
        if quick_reset():
            print("\\n🎉 快速重置成功！")
        else:
            print("\\n❌ 所有重置方法都失败")
            print("\\n建议:")
            print("1. 重启K230开发板")
            print("2. 重新连接CanMV IDE")
            print("3. 检查硬件连接")
    
    show_help()

if __name__ == "__main__":
    main()
