# 庐山派K230重置工具 - 解决传感器占用问题
import time, gc

print("🔄 庐山派K230重置工具")
print("专门解决传感器被占用的问题")

def force_reset_all():
    """强制重置所有资源"""
    print("⚡ 强制重置所有资源...")
    
    try:
        # 导入模块
        from media.sensor import *
        from media.display import *
        from media.media import *
        print("✅ 模块导入成功")
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请重启庐山派K230开发板")
        return False
    
    # 强制停止所有可能的传感器
    print("停止所有传感器...")
    for sensor_id in range(10):  # 尝试更多ID
        try:
            sensor = Sensor(id=sensor_id)
            sensor.stop()
            print(f"✅ 停止传感器ID {sensor_id}")
        except Exception as e:
            if "not inited" not in str(e):
                print(f"传感器ID {sensor_id}: {str(e)}")
    
    # 清理媒体管理器
    try:
        MediaManager.deinit()
        print("✅ 媒体管理器已清理")
    except Exception as e:
        print(f"媒体管理器: {str(e)}")
    
    # 清理显示
    try:
        Display.deinit()
        print("✅ 显示已清理")
    except Exception as e:
        print(f"显示: {str(e)}")
    
    # 多次垃圾回收
    for i in range(3):
        gc.collect()
        time.sleep(1)
    
    print("✅ 强制重置完成")
    return True

def test_sensor_availability():
    """测试传感器可用性"""
    print("🔍 测试传感器可用性...")
    
    try:
        from media.sensor import *
        
        available_sensors = []
        
        # 测试所有可能的传感器ID
        for sensor_id in range(6):
            try:
                print(f"测试传感器ID {sensor_id}...")
                sensor = Sensor(id=sensor_id)
                sensor.reset()
                available_sensors.append(sensor_id)
                print(f"✅ 传感器ID {sensor_id} 可用")
                
                # 立即停止，避免占用
                sensor.stop()
                
            except Exception as e:
                error_msg = str(e)
                if "already inited" in error_msg:
                    print(f"⚠️ 传感器ID {sensor_id} 被占用")
                elif "not support" in error_msg:
                    print(f"❌ 传感器ID {sensor_id} 不支持")
                else:
                    print(f"❌ 传感器ID {sensor_id}: {error_msg}")
        
        if available_sensors:
            print(f"✅ 可用传感器: {available_sensors}")
            return available_sensors[0]
        else:
            print("❌ 没有可用传感器")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return None

def simple_init_test(sensor_id):
    """简单初始化测试"""
    print(f"🚀 简单初始化测试 (传感器ID: {sensor_id})")
    
    try:
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        # 创建传感器
        sensor = Sensor(id=sensor_id)
        sensor.reset()
        
        # 最简单配置
        sensor.set_framesize(Sensor.QQVGA)  # 160x120
        sensor.set_pixformat(Sensor.RGB565)
        
        print("✅ 传感器配置成功")
        
        # 初始化显示
        Display.init(Display.VIRT, 160, 120)
        print("✅ 显示初始化成功")
        
        # 初始化媒体管理器
        MediaManager.init()
        print("✅ 媒体管理器初始化成功")
        
        # 启动传感器
        sensor.run()
        print("✅ 传感器启动成功")
        
        # 等待稳定
        time.sleep(3)
        
        # 测试图像捕获
        img = sensor.snapshot()
        if img:
            print(f"✅ 图像捕获成功: {img.width()}x{img.height()}")
            
            # 简单绘制测试
            img.draw_string(5, 5, "庐山派OK", color=(255, 255, 255), scale=1)
            img.draw_rectangle((10, 20, 40, 30), color=(0, 255, 0), thickness=2)
            
            # 显示
            Display.show_image(img)
            
            print("🎉 庐山派K230初始化完全成功!")
            return True
        else:
            print("❌ 图像捕获失败")
            return False
            
    except Exception as e:
        print(f"❌ 初始化测试失败: {str(e)}")
        return False

def complete_reset_procedure():
    """完整重置流程"""
    print("🏔️ 庐山派K230完整重置流程")
    print("=" * 40)
    
    # 步骤1: 强制重置
    print("\\n步骤1: 强制重置所有资源")
    if not force_reset_all():
        print("❌ 强制重置失败")
        return False
    
    # 等待
    print("等待3秒...")
    time.sleep(3)
    
    # 步骤2: 测试传感器
    print("\\n步骤2: 测试传感器可用性")
    sensor_id = test_sensor_availability()
    if sensor_id is None:
        print("❌ 没有可用传感器")
        print("\\n🔧 请尝试:")
        print("1. 重启庐山派K230开发板")
        print("2. 重新连接CanMV IDE")
        print("3. 检查摄像头连接")
        return False
    
    # 步骤3: 简单初始化测试
    print("\\n步骤3: 简单初始化测试")
    if simple_init_test(sensor_id):
        print("\\n🎉 庐山派K230重置成功!")
        print("✅ 硬件工作正常")
        print("\\n现在可以运行:")
        print("exec(open('lushan_simple_color.py').read())")
        return True
    else:
        print("\\n⚠️ 初始化测试失败")
        print("硬件可能有问题")
        return False

def quick_fix():
    """快速修复"""
    print("⚡ 庐山派K230快速修复")
    
    # 快速清理
    try:
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        # 停止传感器
        for i in [0, 1, 2]:
            try:
                Sensor(id=i).stop()
            except:
                pass
        
        # 清理
        try: MediaManager.deinit()
        except: pass
        try: Display.deinit()
        except: pass
        
        gc.collect()
        time.sleep(2)
        
        print("✅ 快速清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 快速修复失败: {str(e)}")
        return False

def show_help():
    """显示帮助"""
    print("""
🏔️ 庐山派K230重置工具使用说明

🚨 遇到"所有传感器ID初始化失败"时:

1. 🔄 运行完整重置:
   complete_reset_procedure()

2. ⚡ 快速修复:
   quick_fix()

3. 🔧 手动步骤:
   - 重启庐山派K230开发板
   - 重新连接CanMV IDE
   - 运行重置工具

4. 📞 如果仍然失败:
   - 检查摄像头硬件连接
   - 确认电源供电充足
   - 验证固件版本兼容性
""")

def main():
    """主函数"""
    print("🏔️ 庐山派K230重置工具")
    
    show_help()
    
    print("\\n选择操作:")
    print("1. 完整重置流程")
    print("2. 快速修复")
    print("3. 仅测试传感器")
    
    try:
        choice = input("请选择 (1-3): ")
        
        if choice == "1":
            complete_reset_procedure()
        elif choice == "2":
            if quick_fix():
                print("快速修复完成，请尝试运行其他程序")
        elif choice == "3":
            test_sensor_availability()
        else:
            print("自动运行完整重置流程...")
            complete_reset_procedure()
            
    except:
        print("自动运行完整重置流程...")
        complete_reset_procedure()

if __name__ == "__main__":
    main()
