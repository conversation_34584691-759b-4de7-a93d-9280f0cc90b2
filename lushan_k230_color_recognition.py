# 庐山派K230物体颜色识别 - MicroPython版本
# 专为庐山派K230开发板优化的颜色识别系统
import time, gc, math

# 导入配置文件
try:
    from lushan_k230_config import *
    CONFIG_LOADED = True
    print("✅ 庐山派K230配置加载成功")
except ImportError:
    CONFIG_LOADED = False
    print("⚠️ 配置文件未找到，使用默认配置")

# 模块级别导入，避免语法错误
try:
    from media.sensor import *
    from media.display import *
    from media.media import *
    MODULES_OK = True
    print("✅ 庐山派K230模块加载成功")
except ImportError as e:
    print(f"❌ 模块加载失败: {e}")
    MODULES_OK = False

# 庐山派K230专用配置
LUSHAN_CONFIG = {
    'sensor': {
        'id': 0,  # 庐山派K230默认摄像头ID
        'width': 320,  # QVGA分辨率，性能最佳
        'height': 240,
        'fps': 30
    },
    'colors': {  # 优化的颜色阈值(LAB色彩空间)
        'red': {
            'threshold': (30, 100, 15, 127, 15, 127),
            'rgb': (255, 0, 0),
            'name': '红色',
            'en_name': 'Red'
        },
        'green': {
            'threshold': (40, 80, -70, -10, -0, 30),
            'rgb': (0, 255, 0),
            'name': '绿色',
            'en_name': 'Green'
        },
        'blue': {
            'threshold': (0, 30, 0, 64, -128, 0),
            'rgb': (0, 0, 255),
            'name': '蓝色',
            'en_name': 'Blue'
        },
        'yellow': {
            'threshold': (60, 100, -10, 10, 20, 127),
            'rgb': (255, 255, 0),
            'name': '黄色',
            'en_name': 'Yellow'
        },
        'orange': {
            'threshold': (40, 70, 10, 50, 20, 80),
            'rgb': (255, 165, 0),
            'name': '橙色',
            'en_name': 'Orange'
        }
    },
    'detection': {
        'min_area': 150,  # 最小检测面积
        'max_objects': 2,  # 每种颜色最多检测对象数
        'density_threshold': 0.4,  # 密度阈值
        'margin': 5  # 检测框边距
    }
}

class LushanColorRecognizer:
    """庐山派K230颜色识别器"""
    
    def __init__(self):
        self.sensor = None
        self.clock = time.clock()
        self.detection_stats = {color: 0 for color in LUSHAN_CONFIG['colors']}
        self.total_frames = 0
        
    def init_hardware(self):
        """初始化硬件"""
        if not MODULES_OK:
            print("❌ 模块未加载，无法初始化")
            return False
            
        print("🔧 初始化庐山派K230硬件...")
        
        try:
            # 清理可能存在的资源
            self._cleanup_resources()
            
            # 初始化传感器
            sensor_ids = [0, 1, 2]  # 尝试不同传感器ID
            for sensor_id in sensor_ids:
                try:
                    print(f"尝试传感器ID: {sensor_id}")
                    self.sensor = Sensor(id=sensor_id)
                    self.sensor.reset()
                    break
                except Exception as e:
                    print(f"传感器ID {sensor_id} 失败: {str(e)}")
                    continue
            
            if self.sensor is None:
                print("❌ 所有传感器ID初始化失败")
                return False
            
            # 配置传感器
            self.sensor.set_framesize(Sensor.QVGA)  # 320x240
            self.sensor.set_pixformat(Sensor.RGB565)
            
            # 初始化显示
            Display.init(Display.VIRT, LUSHAN_CONFIG['sensor']['width'], 
                        LUSHAN_CONFIG['sensor']['height'])
            
            # 初始化媒体管理器
            MediaManager.init()
            
            # 启动传感器
            self.sensor.run()
            
            print("✅ 庐山派K230硬件初始化成功")
            time.sleep(2)  # 等待稳定
            return True
            
        except Exception as e:
            print(f"❌ 硬件初始化失败: {str(e)}")
            return False
    
    def _cleanup_resources(self):
        """清理资源"""
        try:
            for sensor_id in [0, 1, 2]:
                try:
                    temp_sensor = Sensor(id=sensor_id)
                    temp_sensor.stop()
                except:
                    pass
            MediaManager.deinit()
            Display.deinit()
            gc.collect()
            time.sleep(1)
        except:
            pass
    
    def detect_colors(self, img):
        """检测图像中的颜色"""
        detected_objects = []
        
        for color_name, color_info in LUSHAN_CONFIG['colors'].items():
            try:
                # 查找颜色blob
                blobs = img.find_blobs([color_info['threshold']], 
                                     pixels_threshold=LUSHAN_CONFIG['detection']['min_area'])
                
                # 筛选有效blob
                valid_blobs = []
                for blob in blobs:
                    if (blob.density() > LUSHAN_CONFIG['detection']['density_threshold'] and
                        blob.w() > 10 and blob.h() > 10):
                        valid_blobs.append(blob)
                
                # 按面积排序，取最大的几个
                valid_blobs.sort(key=lambda b: b.pixels(), reverse=True)
                valid_blobs = valid_blobs[:LUSHAN_CONFIG['detection']['max_objects']]
                
                # 添加到检测结果
                for blob in valid_blobs:
                    detected_objects.append({
                        'color_name': color_name,
                        'color_info': color_info,
                        'blob': blob,
                        'area': blob.pixels(),
                        'center': (blob.cx(), blob.cy()),
                        'size': (blob.w(), blob.h())
                    })
                    
                # 更新统计
                if valid_blobs:
                    self.detection_stats[color_name] += 1
                    
            except Exception as e:
                print(f"检测{color_name}时出错: {str(e)}")
                continue
        
        return detected_objects
    
    def draw_detection_results(self, img, detected_objects):
        """绘制检测结果"""
        margin = LUSHAN_CONFIG['detection']['margin']
        
        # 绘制检测框和标签
        for obj in detected_objects:
            blob = obj['blob']
            color_info = obj['color_info']
            rgb_color = color_info['rgb']
            color_name = color_info['name']
            
            # 绘制检测框
            rect = (blob.x() - margin, blob.y() - margin, 
                   blob.w() + 2*margin, blob.h() + 2*margin)
            img.draw_rectangle(rect, color=rgb_color, thickness=2)
            
            # 绘制中心十字
            img.draw_cross(blob.cx(), blob.cy(), color=rgb_color, size=8, thickness=2)
            
            # 绘制标签
            label_x = max(0, blob.x() - margin)
            label_y = max(12, blob.y() - margin - 2)
            
            # 背景框
            label_text = f"{color_name}"
            img.draw_rectangle((label_x, label_y-12, len(label_text)*8, 12), 
                             color=(0, 0, 0), thickness=-1, fill=True)
            
            # 文字
            img.draw_string(label_x+2, label_y-10, label_text, 
                          color=rgb_color, scale=1)
            
            # 坐标信息
            coord_text = f"({blob.cx()},{blob.cy()})"
            img.draw_string(label_x+2, label_y+2, coord_text, 
                          color=rgb_color, scale=1)
    
    def draw_color_palette(self, img, detected_objects):
        """绘制颜色调色板"""
        palette_x = img.width() - 60
        palette_y = 10
        block_size = 25
        
        # 绘制检测到的颜色块
        unique_colors = {}
        for obj in detected_objects:
            color_name = obj['color_name']
            if color_name not in unique_colors:
                unique_colors[color_name] = obj['color_info']
        
        for i, (color_name, color_info) in enumerate(unique_colors.items()):
            y = palette_y + i * (block_size + 3)
            rgb_color = color_info['rgb']
            
            # 绘制颜色块
            img.draw_rectangle((palette_x, y, block_size, block_size), 
                             color=rgb_color, thickness=-1, fill=True)
            img.draw_rectangle((palette_x, y, block_size, block_size), 
                             color=(255, 255, 255), thickness=1)
            
            # 颜色名称
            img.draw_string(palette_x+2, y+8, color_info['name'], 
                          color=(255, 255, 255), scale=1)
    
    def draw_status_info(self, img, detected_objects, fps):
        """绘制状态信息"""
        # 基本信息
        img.draw_string(5, 5, "庐山派K230颜色识别", color=(255, 255, 255), scale=1)
        img.draw_string(5, 18, f"FPS: {fps:.1f}", color=(255, 255, 255), scale=1)
        img.draw_string(5, 31, f"检测: {len(detected_objects)}个对象", color=(255, 255, 255), scale=1)
        
        # 检测统计
        stats_y = 44
        for color_name, count in self.detection_stats.items():
            if count > 0:
                color_info = LUSHAN_CONFIG['colors'][color_name]
                stats_text = f"{color_info['name']}: {count}"
                img.draw_string(5, stats_y, stats_text, color=color_info['rgb'], scale=1)
                stats_y += 13
    
    def run_recognition(self, duration=30):
        """运行颜色识别"""
        if not self.sensor:
            print("❌ 传感器未初始化")
            return False
        
        print(f"🎨 开始庐山派K230颜色识别 (运行{duration}秒)...")
        print("支持颜色: 红色、绿色、蓝色、黄色、橙色")
        
        start_time = time.time()
        frame_count = 0
        
        try:
            while time.time() - start_time < duration:
                self.clock.tick()
                
                # 捕获图像
                img = self.sensor.snapshot()
                if not img:
                    print("图像捕获失败")
                    continue
                
                frame_count += 1
                self.total_frames += 1
                
                # 检测颜色
                detected_objects = self.detect_colors(img)
                
                # 绘制结果
                if detected_objects:
                    self.draw_detection_results(img, detected_objects)
                    self.draw_color_palette(img, detected_objects)
                    
                    # 打印检测信息
                    colors_found = set(obj['color_name'] for obj in detected_objects)
                    colors_str = ', '.join([LUSHAN_CONFIG['colors'][c]['name'] for c in colors_found])
                    print(f"第{frame_count}帧: 检测到 {colors_str}")
                
                # 绘制状态信息
                self.draw_status_info(img, detected_objects, self.clock.fps())
                
                # 显示图像
                Display.show_image(img)
                
                # 内存管理
                if frame_count % 10 == 0:
                    gc.collect()
                
                time.sleep(0.05)  # 控制帧率
            
            print(f"\\n✅ 颜色识别完成")
            print(f"总帧数: {frame_count}")
            print(f"平均FPS: {frame_count/duration:.1f}")
            
            # 打印检测统计
            print("\\n📊 检测统计:")
            for color_name, count in self.detection_stats.items():
                if count > 0:
                    color_info = LUSHAN_CONFIG['colors'][color_name]
                    percentage = (count / frame_count) * 100
                    print(f"{color_info['name']}: {count}次 ({percentage:.1f}%)")
            
            return True
            
        except Exception as e:
            print(f"❌ 颜色识别出错: {str(e)}")
            return False
    
    def test_basic_function(self):
        """测试基本功能"""
        print("🧪 测试庐山派K230基本功能...")
        
        if not self.sensor:
            return False
        
        try:
            for i in range(5):
                img = self.sensor.snapshot()
                if img:
                    # 简单绘制
                    img.draw_string(10, 10, f"庐山派K230测试 {i+1}/5", color=(255, 255, 255), scale=1)
                    img.draw_rectangle((50, 50, 80, 60), color=(0, 255, 0), thickness=2)
                    img.draw_cross(90, 80, color=(255, 0, 0), size=10, thickness=2)
                    
                    Display.show_image(img)
                    print(f"✅ 第{i+1}次测试成功: {img.width()}x{img.height()}")
                else:
                    print(f"❌ 第{i+1}次测试失败")
                
                time.sleep(0.5)
            
            print("✅ 基本功能测试完成")
            return True
            
        except Exception as e:
            print(f"❌ 基本功能测试失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("🚀 庐山派K230物体颜色识别系统")
    print("专为庐山派K230开发板优化")
    
    if not MODULES_OK:
        print("❌ 模块加载失败，请检查:")
        print("1. 庐山派K230固件版本")
        print("2. CanMV IDE连接")
        print("3. 开发板状态")
        return
    
    # 创建识别器
    recognizer = LushanColorRecognizer()
    
    # 初始化硬件
    if not recognizer.init_hardware():
        print("❌ 硬件初始化失败")
        return
    
    # 测试基本功能
    if recognizer.test_basic_function():
        print("\\n✅ 基本功能正常，开始颜色识别...")
        
        # 运行颜色识别
        recognizer.run_recognition(duration=20)  # 运行20秒
    else:
        print("❌ 基本功能测试失败")
    
    print("\\n程序结束")

if __name__ == "__main__":
    main()
