# K230 CanMV官方API颜色检测器
from media.camera import *  # K230官方camera模块
from media.display import *  # K230官方display模块
from media.media import *  # K230官方media模块
import time, gc
import image

# K230官方API配置
OFFICIAL_CONFIG = {
    'camera': {
        'dev_id': CAM_DEV_ID_0,  # 摄像头设备ID
        'sensor_type': CAM_DEFAULT_SENSOR,  # 默认传感器OV5647
        'out_width': 640,  # 输出宽度
        'out_height': 480,  # 输出高度
        'chn_id': CAM_CHN_ID_0  # 输出通道ID
    },
    'display': {
        'type': LT9611_1920X1080_30FPS,  # HDMI显示类型
        'plane_id': 0  # 显示平面ID
    },
    'colors': {  # 颜色检测配置
        'red': [(30, 100, 15, 127, 15, 127), (255, 0, 0), '红色'],
        'green': [(40, 80, -70, -10, -0, 30), (0, 255, 0), '绿色'],
        'blue': [(0, 30, 0, 64, -128, 0), (0, 0, 255), '蓝色']
    },
    'detect': {'min_area': 300, 'max_objects': 2}  # 检测参数
}

class K230OfficialColorDetector:
    def __init__(self):
        self.media_source = None  # 媒体源设备
        self.media_sink = None  # 媒体接收设备
        self.detected_objects = []  # 检测到的对象
        self.frame_count = 0  # 帧计数
        
    def init_camera_display(self):
        """初始化摄像头和显示 - 使用K230官方API"""
        try:
            # 初始化HDMI显示
            display.init(OFFICIAL_CONFIG['display']['type'])
            print("✅ HDMI显示初始化成功")
            
            # 初始化摄像头传感器
            camera.sensor_init(OFFICIAL_CONFIG['camera']['dev_id'], 
                             OFFICIAL_CONFIG['camera']['sensor_type'])
            print("✅ 摄像头传感器初始化成功")
            
            # 设置输出尺寸（16字节对齐）
            out_width = ALIGN_UP(OFFICIAL_CONFIG['camera']['out_width'], 16)
            out_height = OFFICIAL_CONFIG['camera']['out_height']
            
            # 配置摄像头输出
            camera.set_outsize(OFFICIAL_CONFIG['camera']['dev_id'], 
                             OFFICIAL_CONFIG['camera']['chn_id'], 
                             out_width, out_height)
            
            # 设置输出格式为RGB888，便于颜色检测
            camera.set_outfmt(OFFICIAL_CONFIG['camera']['dev_id'], 
                             OFFICIAL_CONFIG['camera']['chn_id'], 
                             PIXEL_FORMAT_RGB_888)
            
            # 创建媒体链路
            self.media_source = media_device(CAMERA_MOD_ID, 
                                           OFFICIAL_CONFIG['camera']['dev_id'], 
                                           OFFICIAL_CONFIG['camera']['chn_id'])
            
            self.media_sink = media_device(DISPLAY_MOD_ID, 
                                         DISPLAY_DEV_ID, 
                                         DISPLAY_CHN_VIDEO1)
            
            # 建立媒体链路
            media.create_link(self.media_source, self.media_sink)
            
            # 设置显示平面属性
            display.set_plane(0, 0, out_width, out_height, 
                            PIXEL_FORMAT_YVU_PLANAR_420, 
                            DISPLAY_MIRROR_NONE, 
                            DISPLAY_CHN_VIDEO1)
            
            # 初始化媒体缓冲区
            ret = media.buffer_init()
            if ret:
                print("❌ 媒体缓冲区初始化失败")
                return False
                
            print("✅ K230官方API初始化完成")
            return True
            
        except Exception as e:
            print("❌ 初始化失败:", str(e))
            return False
            
    def start_camera_stream(self):
        """启动摄像头数据流"""
        try:
            camera.start_stream(OFFICIAL_CONFIG['camera']['dev_id'])
            print("✅ 摄像头数据流启动成功")
            time.sleep(2)  # 等待摄像头稳定
            return True
        except Exception as e:
            print("❌ 启动摄像头失败:", str(e))
            return False
            
    def detect_colors_in_image(self, img):
        """在图像中检测颜色"""
        self.detected_objects.clear()
        
        for color_name, (hsv_threshold, rgb_color, cn_name) in OFFICIAL_CONFIG['colors'].items():
            try:
                # 使用find_blobs检测颜色
                blobs = img.find_blobs([hsv_threshold], 
                                     pixels_threshold=OFFICIAL_CONFIG['detect']['min_area'])
                
                for blob in blobs[:OFFICIAL_CONFIG['detect']['max_objects']]:
                    if blob.density() > 0.4:  # 密度过滤
                        self.detected_objects.append({
                            'blob': blob,
                            'color_name': color_name,
                            'rgb_color': rgb_color,
                            'cn_name': cn_name
                        })
                        
            except Exception as e:
                print(f"检测{color_name}时出错:", str(e))
                continue
                
    def draw_detection_results(self, img):
        """绘制检测结果"""
        for obj in self.detected_objects:
            blob = obj['blob']
            rgb_color = obj['rgb_color']
            cn_name = obj['cn_name']
            
            try:
                # 绘制矩形框
                img.draw_rectangle(blob.rect(), color=rgb_color, thickness=2)
                
                # 绘制中心点
                img.draw_cross(blob.cx(), blob.cy(), color=rgb_color, size=8, thickness=2)
                
                # 显示标签
                label = f"{cn_name}({blob.cx()},{blob.cy()})"
                img.draw_string(blob.x(), blob.y()-15, label, color=rgb_color, scale=1)
                
            except Exception as e:
                print("绘制检测结果出错:", str(e))
                continue
                
    def generate_color_rectangles(self, img):
        """生成颜色矩形块"""
        rect_w, rect_h = 60, 30
        start_x = OFFICIAL_CONFIG['camera']['out_width'] - rect_w - 10
        
        for i, obj in enumerate(self.detected_objects[:3]):  # 最多3个
            y = 10 + i * (rect_h + 5)
            rgb_color = obj['rgb_color']
            cn_name = obj['cn_name']
            
            try:
                # 绘制填充矩形
                img.draw_rectangle((start_x, y, rect_w, rect_h), 
                                 color=rgb_color, thickness=-1, fill=True)
                
                # 绘制边框
                img.draw_rectangle((start_x, y, rect_w, rect_h), 
                                 color=(255, 255, 255), thickness=1)
                
                # 显示颜色名称
                img.draw_string(start_x+5, y+8, cn_name, color=(255, 255, 255), scale=1)
                
            except Exception as e:
                print("生成颜色矩形出错:", str(e))
                continue
                
    def draw_status_info(self, img):
        """绘制状态信息"""
        try:
            # 帧数显示
            frame_text = f"帧数: {self.frame_count}"
            img.draw_string(10, 10, frame_text, color=(255, 255, 255), scale=1)
            
            # 检测数量
            count_text = f"检测: {len(self.detected_objects)}个"
            img.draw_string(10, 30, count_text, color=(255, 255, 255), scale=1)
            
            # 内存信息
            mem_free = gc.mem_free()
            mem_text = f"内存: {mem_free//1024}KB"
            img.draw_string(10, 50, mem_text, color=(255, 255, 255), scale=1)
            
        except Exception as e:
            print("绘制状态信息出错:", str(e))
            
    def run_detection_loop(self):
        """运行检测循环"""
        print("开始颜色检测循环...")
        
        try:
            while True:
                # 捕获图像
                img = camera.capture_image(OFFICIAL_CONFIG['camera']['dev_id'], 
                                         OFFICIAL_CONFIG['camera']['chn_id'])
                
                if img == -1:
                    print("捕获图像失败")
                    time.sleep(0.1)
                    continue
                    
                self.frame_count += 1
                
                # 执行颜色检测
                self.detect_colors_in_image(img)
                
                # 绘制检测结果
                self.draw_detection_results(img)
                
                # 生成颜色矩形块
                self.generate_color_rectangles(img)
                
                # 绘制状态信息
                self.draw_status_info(img)
                
                # 释放图像资源
                camera.release_image(OFFICIAL_CONFIG['camera']['dev_id'], 
                                   OFFICIAL_CONFIG['camera']['chn_id'], img)
                
                # 内存管理
                gc.collect()
                time.sleep(0.05)  # 控制帧率
                
        except KeyboardInterrupt:
            print("用户停止检测")
        except Exception as e:
            print("检测循环出错:", str(e))
        finally:
            self.cleanup()
            
    def cleanup(self):
        """清理资源"""
        try:
            # 停止摄像头
            camera.stop_stream(OFFICIAL_CONFIG['camera']['dev_id'])
            
            # 去初始化显示
            display.deinit()
            
            # 销毁媒体链路
            if self.media_source and self.media_sink:
                media.destroy_link(self.media_source, self.media_sink)
            
            # 去初始化媒体缓冲区
            media.buffer_deinit()
            
            print("✅ 资源清理完成")
            
        except Exception as e:
            print("清理资源出错:", str(e))

def simple_color_test():
    """简单颜色检测测试"""
    print("启动简单颜色检测测试...")
    
    try:
        # 初始化显示
        display.init(LT9611_1920X1080_30FPS)
        
        # 初始化摄像头
        camera.sensor_init(CAM_DEV_ID_0, CAM_DEFAULT_SENSOR)
        
        # 设置输出
        out_width = ALIGN_UP(320, 16)
        out_height = 240
        camera.set_outsize(CAM_DEV_ID_0, CAM_CHN_ID_0, out_width, out_height)
        camera.set_outfmt(CAM_DEV_ID_0, CAM_CHN_ID_0, PIXEL_FORMAT_RGB_888)
        
        # 初始化媒体缓冲区
        media.buffer_init()
        
        # 启动摄像头
        camera.start_stream(CAM_DEV_ID_0)
        time.sleep(2)
        
        # 红色检测阈值
        red_threshold = (30, 100, 15, 127, 15, 127)
        
        print("简单红色检测开始...")
        
        for i in range(50):  # 检测50帧
            img = camera.capture_image(CAM_DEV_ID_0, CAM_CHN_ID_0)
            
            if img != -1:
                # 检测红色
                blobs = img.find_blobs([red_threshold], pixels_threshold=200)
                
                if blobs:
                    largest = max(blobs, key=lambda b: b.pixels())
                    # 绘制检测框
                    img.draw_rectangle(largest.rect(), color=(255, 0, 0), thickness=2)
                    # 绘制红色矩形块
                    img.draw_rectangle((250, 10, 50, 25), color=(255, 0, 0), thickness=-1, fill=True)
                    img.draw_rectangle((250, 10, 50, 25), color=(255, 255, 255), thickness=1)
                    
                img.draw_string(10, 10, f"简单测试 {i+1}/50", color=(255, 255, 255), scale=1)
                
                # 释放图像
                camera.release_image(CAM_DEV_ID_0, CAM_CHN_ID_0, img)
                
            time.sleep(0.1)
            gc.collect()
            
        print("简单测试完成")
        
    except Exception as e:
        print("简单测试出错:", str(e))
    finally:
        try:
            camera.stop_stream(CAM_DEV_ID_0)
            display.deinit()
            media.buffer_deinit()
        except:
            pass

def main():
    """主函数"""
    print("=== K230官方API颜色检测器 ===")
    
    try:
        # 创建检测器
        detector = K230OfficialColorDetector()
        
        # 初始化硬件
        if detector.init_camera_display():
            # 启动摄像头
            if detector.start_camera_stream():
                # 运行检测
                detector.run_detection_loop()
            else:
                print("摄像头启动失败，尝试简单测试...")
                simple_color_test()
        else:
            print("硬件初始化失败，尝试简单测试...")
            simple_color_test()
            
    except Exception as e:
        print("主程序出错:", str(e))

if __name__ == "__main__":
    main()
