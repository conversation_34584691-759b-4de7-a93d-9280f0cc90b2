# K230 CanMV颜色检测器 - 正确的导入方式
from media.sensor import *  # K230 CanMV正确的sensor导入方式
from media.display import *  # K230 CanMV显示模块
from media.media import *  # K230 CanMV媒体模块
import time, gc, os
import image  # 图像处理模块

# K230 CanMV配置参数
CANMV_CONFIG = {
    'sensor': {'width': 640, 'height': 480, 'format': PIXEL_FORMAT_RGB_888_PLANAR},  # 传感器配置
    'display': {'width': 1920, 'height': 1080, 'format': PIXEL_FORMAT_YUV_SEMIPLANAR_420},  # 显示配置
    'detect': {'min_area': 500, 'max_objects': 3},  # 检测参数
    'colors': {  # 颜色阈值配置
        'red': [(30, 100, 15, 127, 15, 127), (255, 0, 0), '红色'],
        'green': [(40, 80, -70, -10, -0, 30), (0, 255, 0), '绿色'],
        'blue': [(0, 30, 0, 64, -128, 0), (0, 0, 255), '蓝色'],
        'yellow': [(20, 100, -10, 10, 20, 127), (255, 255, 0), '黄色']
    }
}

class K230CanMVColorDetector:
    def __init__(self):
        self.sensor = None  # 传感器对象
        self.display = None  # 显示对象
        self.detected_objects = []  # 检测到的对象
        self.fps_counter = 0  # FPS计数器
        self.last_fps_time = time.ticks_ms()  # 上次FPS计算时间
        self.current_fps = 0  # 当前FPS
        
    def init_hardware(self):
        """初始化K230 CanMV硬件"""
        try:
            # 初始化媒体管理器
            media_init()
            
            # 初始化传感器
            self.sensor = Sensor(id=0)  # 使用传感器0
            self.sensor.reset()
            
            # 配置传感器
            self.sensor.set_framesize(width=CANMV_CONFIG['sensor']['width'], 
                                    height=CANMV_CONFIG['sensor']['height'])
            self.sensor.set_pixformat(CANMV_CONFIG['sensor']['format'])
            
            # 初始化显示
            self.display = Display()
            self.display.init(lt9611_1920x1080_30fps)  # 使用HDMI输出
            
            # 绑定传感器和显示
            bind_info = self.sensor.bind_info()
            self.display.bind_layer(**bind_info, layer=Display.LAYER_VIDEO1)
            
            # 启动传感器
            self.sensor.run()
            
            print("K230 CanMV硬件初始化成功")
            return True
            
        except Exception as e:
            print("硬件初始化失败:", str(e))
            return False
            
    def detect_colors(self, img):
        """检测多种颜色"""
        self.detected_objects.clear()
        
        for color_name, (hsv_threshold, rgb_color, cn_name) in CANMV_CONFIG['colors'].items():
            try:
                # 使用find_blobs检测颜色
                blobs = img.find_blobs([hsv_threshold], 
                                     pixels_threshold=CANMV_CONFIG['detect']['min_area'],
                                     area_threshold=CANMV_CONFIG['detect']['min_area'])
                
                # 处理检测到的色块
                for blob in blobs[:CANMV_CONFIG['detect']['max_objects']]:
                    if blob.density() > 0.5:  # 密度过滤
                        self.detected_objects.append({
                            'blob': blob,
                            'color_name': color_name,
                            'rgb_color': rgb_color,
                            'cn_name': cn_name
                        })
                        
            except Exception as e:
                print(f"检测{color_name}时出错:", str(e))
                continue
                
    def draw_detection_results(self, img):
        """绘制检测结果"""
        for obj in self.detected_objects:
            blob = obj['blob']
            rgb_color = obj['rgb_color']
            cn_name = obj['cn_name']
            
            try:
                # 绘制矩形框
                img.draw_rectangle(blob.rect(), color=rgb_color, thickness=3)
                
                # 绘制中心十字
                img.draw_cross(blob.cx(), blob.cy(), color=rgb_color, size=10, thickness=2)
                
                # 显示标签信息
                label = f"{cn_name}:({blob.cx()},{blob.cy()})"
                img.draw_string(blob.x(), blob.y()-20, label, color=rgb_color, scale=2)
                
            except Exception as e:
                print("绘制检测结果时出错:", str(e))
                continue
                
    def generate_color_rectangles(self, img):
        """生成对应颜色的矩形块"""
        rect_width, rect_height = 80, 50
        start_x = CANMV_CONFIG['sensor']['width'] - rect_width - 10
        
        for i, obj in enumerate(self.detected_objects[:4]):  # 最多显示4个
            rect_y = 10 + i * (rect_height + 10)
            rgb_color = obj['rgb_color']
            cn_name = obj['cn_name']
            
            try:
                # 绘制填充矩形
                img.draw_rectangle((start_x, rect_y, rect_width, rect_height), 
                                 color=rgb_color, thickness=-1, fill=True)
                
                # 绘制白色边框
                img.draw_rectangle((start_x, rect_y, rect_width, rect_height), 
                                 color=(255, 255, 255), thickness=2)
                
                # 显示颜色名称
                img.draw_string(start_x+5, rect_y+15, cn_name, color=(255, 255, 255), scale=2)
                
            except Exception as e:
                print("生成颜色矩形时出错:", str(e))
                continue
                
    def calculate_fps(self):
        """计算FPS"""
        self.fps_counter += 1
        current_time = time.ticks_ms()
        
        if time.ticks_diff(current_time, self.last_fps_time) >= 1000:  # 每秒更新一次
            self.current_fps = self.fps_counter
            self.fps_counter = 0
            self.last_fps_time = current_time
            
    def draw_status_info(self, img):
        """绘制状态信息"""
        try:
            # FPS显示
            fps_text = f"FPS: {self.current_fps}"
            img.draw_string(10, 10, fps_text, color=(255, 255, 255), scale=2)
            
            # 检测对象数量
            count_text = f"检测到: {len(self.detected_objects)}个对象"
            img.draw_string(10, 40, count_text, color=(255, 255, 255), scale=2)
            
            # 内存使用情况
            mem_free = gc.mem_free()
            mem_text = f"可用内存: {mem_free//1024}KB"
            img.draw_string(10, 70, mem_text, color=(255, 255, 255), scale=2)
            
        except Exception as e:
            print("绘制状态信息时出错:", str(e))
            
    def run_detection_loop(self):
        """运行检测循环"""
        print("开始K230 CanMV颜色检测...")
        print("支持颜色:", [info[2] for info in CANMV_CONFIG['colors'].values()])
        
        try:
            while True:
                # 获取图像
                rgb888_img = self.sensor.snapshot()
                if rgb888_img is None:
                    print("获取图像失败")
                    continue
                    
                # 转换为RGB565格式进行处理
                img = rgb888_img.to_rgb565()
                
                # 执行颜色检测
                self.detect_colors(img)
                
                # 绘制检测结果
                self.draw_detection_results(img)
                
                # 生成颜色矩形块
                self.generate_color_rectangles(img)
                
                # 计算FPS
                self.calculate_fps()
                
                # 绘制状态信息
                self.draw_status_info(img)
                
                # 显示图像
                self.display.show_image(rgb888_img)
                
                # 内存管理
                gc.collect()
                
        except KeyboardInterrupt:
            print("用户停止检测")
        except Exception as e:
            print("检测循环出错:", str(e))
        finally:
            self.cleanup()
            
    def cleanup(self):
        """清理资源"""
        try:
            if self.sensor:
                self.sensor.stop()
            if self.display:
                self.display.deinit()
            media_deinit()
            print("资源清理完成")
        except Exception as e:
            print("清理资源时出错:", str(e))

def simple_color_test():
    """简单颜色检测测试"""
    print("启动简单颜色检测测试...")
    
    try:
        # 初始化
        media_init()
        sensor = Sensor(id=0)
        sensor.reset()
        sensor.set_framesize(width=640, height=480)
        sensor.set_pixformat(PIXEL_FORMAT_RGB_888_PLANAR)
        
        display = Display()
        display.init(lt9611_1920x1080_30fps)
        
        bind_info = sensor.bind_info()
        display.bind_layer(**bind_info, layer=Display.LAYER_VIDEO1)
        
        sensor.run()
        
        # 红色检测阈值
        red_threshold = (30, 100, 15, 127, 15, 127)
        
        print("简单红色检测开始，按Ctrl+C停止...")
        
        for i in range(100):  # 检测100帧
            rgb888_img = sensor.snapshot()
            if rgb888_img is None:
                continue
                
            img = rgb888_img.to_rgb565()
            
            # 检测红色
            blobs = img.find_blobs([red_threshold], pixels_threshold=200)
            
            if blobs:
                largest_blob = max(blobs, key=lambda b: b.pixels())
                # 绘制检测框
                img.draw_rectangle(largest_blob.rect(), color=(255, 0, 0), thickness=3)
                # 生成红色矩形
                img.draw_rectangle((550, 10, 80, 50), color=(255, 0, 0), thickness=-1, fill=True)
                img.draw_rectangle((550, 10, 80, 50), color=(255, 255, 255), thickness=2)
                
            img.draw_string(10, 10, f"简单测试 {i+1}/100", color=(255, 255, 255), scale=2)
            display.show_image(rgb888_img)
            
            time.sleep_ms(100)
            gc.collect()
            
        print("简单测试完成")
        
    except Exception as e:
        print("简单测试出错:", str(e))
    finally:
        try:
            sensor.stop()
            display.deinit()
            media_deinit()
        except:
            pass

def main():
    """主函数"""
    print("=== K230 CanMV颜色检测器 ===")
    
    try:
        # 创建检测器实例
        detector = K230CanMVColorDetector()
        
        # 初始化硬件
        if detector.init_hardware():
            # 运行检测循环
            detector.run_detection_loop()
        else:
            print("硬件初始化失败，启动简单测试...")
            simple_color_test()
            
    except Exception as e:
        print("主程序出错:", str(e))
        print("尝试运行简单测试...")
        simple_color_test()

if __name__ == "__main__":
    main()
