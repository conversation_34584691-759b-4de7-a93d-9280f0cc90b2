# K230修复版颜色检测器 - 解决sensor already inited问题
import time, os, sys, gc

def cleanup_sensor():
    """清理已存在的传感器"""
    try:
        # 尝试导入并清理
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        # 尝试停止可能存在的传感器
        try:
            # 如果有全局sensor变量，尝试停止
            if 'sensor' in globals():
                globals()['sensor'].stop()
                print("✅ 停止了全局sensor")
        except:
            pass
            
        # 尝试清理媒体管理器
        try:
            MediaManager.deinit()
            print("✅ 媒体管理器已清理")
        except:
            pass
            
        # 尝试清理显示
        try:
            Display.deinit()
            print("✅ 显示已清理")
        except:
            pass
            
        # 垃圾回收
        gc.collect()
        time.sleep(1)
        print("✅ 传感器清理完成")
        return True
        
    except Exception as e:
        print("清理传感器时出错:", str(e))
        return False

def safe_sensor_init():
    """安全的传感器初始化"""
    try:
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        # 清理现有资源
        cleanup_sensor()
        
        # 尝试不同的传感器ID
        sensor_ids = [2, 0, 1]  # 优先使用默认的2，然后尝试0和1
        sensor = None
        
        for sensor_id in sensor_ids:
            try:
                print(f"尝试初始化传感器ID: {sensor_id}")
                sensor = Sensor(id=sensor_id)
                sensor.reset()
                print(f"✅ 传感器ID {sensor_id} 初始化成功")
                break
            except Exception as e:
                print(f"❌ 传感器ID {sensor_id} 初始化失败: {str(e)}")
                continue
                
        if sensor is None:
            print("❌ 所有传感器ID都初始化失败")
            return None
            
        # 配置传感器
        sensor.set_framesize(Sensor.QVGA)  # 320x240，较小分辨率
        sensor.set_pixformat(Sensor.RGB565)
        
        # 初始化显示
        Display.init(Display.VIRT, sensor.width(), sensor.height())
        
        # 初始化媒体管理器
        MediaManager.init()
        
        # 启动传感器
        sensor.run()
        
        # 等待稳定
        time.sleep(2)
        
        print("✅ 传感器安全初始化完成")
        return sensor
        
    except Exception as e:
        print("❌ 安全传感器初始化失败:", str(e))
        return None

def test_sensor_capture(sensor, test_count=5):
    """测试传感器捕获"""
    print(f"\\n=== 测试传感器捕获 ({test_count}次) ===")
    
    success_count = 0
    
    for i in range(test_count):
        try:
            img = sensor.snapshot()
            if img:
                print(f"✅ 第{i+1}次捕获成功: {img.width()}x{img.height()}")
                
                # 简单绘制测试
                img.draw_string(10, 10, f"测试 {i+1}/{test_count}", color=(255, 255, 255), scale=1)
                img.draw_rectangle((50, 50, 80, 40), color=(0, 255, 0), thickness=2)
                
                # 显示图像
                Display.show_image(img)
                success_count += 1
            else:
                print(f"❌ 第{i+1}次捕获失败: 返回None")
                
        except Exception as e:
            print(f"❌ 第{i+1}次捕获异常: {str(e)}")
            
        time.sleep(0.5)
        gc.collect()
    
    print(f"捕获测试结果: {success_count}/{test_count}")
    return success_count > 0

def simple_color_detection_fixed(sensor):
    """修复版简单颜色检测"""
    print("\\n=== 修复版颜色检测 ===")
    
    try:
        # 红色阈值 (LAB色彩空间)
        red_threshold = (30, 100, 15, 127, 15, 127)
        
        clock = time.clock()
        detection_count = 0
        
        for i in range(15):  # 检测15帧
            try:
                clock.tick()
                img = sensor.snapshot()
                
                if img:
                    # 检测红色
                    blobs = img.find_blobs([red_threshold], pixels_threshold=50)
                    
                    detected_count = len(blobs)
                    if detected_count > 0:
                        detection_count += 1
                    
                    # 绘制检测结果
                    for j, blob in enumerate(blobs[:2]):  # 最多显示2个
                        img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
                        img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 0), size=5, thickness=2)
                        
                        # 显示坐标
                        coord_text = f"({blob.cx()},{blob.cy()})"
                        img.draw_string(blob.x(), blob.y()-12, coord_text, color=(255, 0, 0), scale=1)
                    
                    # 绘制状态信息
                    img.draw_string(10, 10, f"检测 {i+1}/15", color=(255, 255, 255), scale=1)
                    img.draw_string(10, 25, f"红色: {detected_count}个", color=(255, 255, 255), scale=1)
                    img.draw_string(10, 40, f"FPS: {clock.fps():.1f}", color=(255, 255, 255), scale=1)
                    
                    # 如果检测到红色，在右上角绘制红色矩形块
                    if detected_count > 0:
                        rect_x = sensor.width() - 60
                        rect_y = 10
                        img.draw_rectangle((rect_x, rect_y, 50, 25), color=(255, 0, 0), thickness=-1, fill=True)
                        img.draw_rectangle((rect_x, rect_y, 50, 25), color=(255, 255, 255), thickness=1)
                        img.draw_string(rect_x+15, rect_y+8, "红", color=(255, 255, 255), scale=1)
                        
                        print(f"第{i+1}次检测到{detected_count}个红色对象")
                    
                    # 显示图像
                    Display.show_image(img)
                else:
                    print(f"第{i+1}次图像捕获失败")
                    
            except Exception as e:
                print(f"第{i+1}次检测出错: {str(e)}")
                
            time.sleep(0.3)
            gc.collect()
        
        print(f"\\n✅ 颜色检测完成")
        print(f"总共{15}帧中有{detection_count}帧检测到红色对象")
        return True
        
    except Exception as e:
        print("❌ 颜色检测失败:", str(e))
        return False

def multi_color_detection_fixed(sensor):
    """修复版多颜色检测"""
    print("\\n=== 修复版多颜色检测 ===")
    
    try:
        # 颜色阈值配置
        colors = {
            'red': [(30, 100, 15, 127, 15, 127), (255, 0, 0), '红'],
            'green': [(40, 80, -70, -10, -0, 30), (0, 255, 0), '绿'],
            'blue': [(0, 30, 0, 64, -128, 0), (0, 0, 255), '蓝']
        }
        
        clock = time.clock()
        
        for i in range(20):  # 检测20帧
            try:
                clock.tick()
                img = sensor.snapshot()
                
                if img:
                    detected_objects = []
                    
                    # 检测所有颜色
                    for color_name, (threshold, rgb_color, cn_name) in colors.items():
                        try:
                            blobs = img.find_blobs([threshold], pixels_threshold=50)
                            
                            for blob in blobs[:1]:  # 每种颜色最多1个
                                if blob.density() > 0.3:
                                    detected_objects.append({
                                        'blob': blob,
                                        'rgb_color': rgb_color,
                                        'cn_name': cn_name
                                    })
                                    
                                    # 绘制检测框
                                    img.draw_rectangle(blob.rect(), color=rgb_color, thickness=2)
                                    img.draw_cross(blob.cx(), blob.cy(), color=rgb_color, size=5, thickness=2)
                                    
                                    # 显示标签
                                    label = f"{cn_name}({blob.cx()},{blob.cy()})"
                                    img.draw_string(blob.x(), blob.y()-12, label, color=rgb_color, scale=1)
                                    
                        except Exception as e:
                            print(f"检测{color_name}时出错: {str(e)}")
                            continue
                    
                    # 绘制状态信息
                    img.draw_string(10, 10, f"多色检测 {i+1}/20", color=(255, 255, 255), scale=1)
                    img.draw_string(10, 25, f"检测到: {len(detected_objects)}个", color=(255, 255, 255), scale=1)
                    img.draw_string(10, 40, f"FPS: {clock.fps():.1f}", color=(255, 255, 255), scale=1)
                    
                    # 在右侧绘制检测到的颜色矩形块
                    rect_w, rect_h = 40, 20
                    start_x = sensor.width() - rect_w - 10
                    
                    for j, obj in enumerate(detected_objects[:3]):  # 最多显示3个
                        y = 10 + j * (rect_h + 5)
                        rgb_color = obj['rgb_color']
                        cn_name = obj['cn_name']
                        
                        # 绘制填充矩形
                        img.draw_rectangle((start_x, y, rect_w, rect_h), 
                                         color=rgb_color, thickness=-1, fill=True)
                        # 绘制边框
                        img.draw_rectangle((start_x, y, rect_w, rect_h), 
                                         color=(255, 255, 255), thickness=1)
                        # 显示颜色名称
                        img.draw_string(start_x+10, y+6, cn_name, color=(255, 255, 255), scale=1)
                    
                    if detected_objects:
                        print(f"第{i+1}次检测到{len(detected_objects)}个颜色对象")
                    
                    # 显示图像
                    Display.show_image(img)
                else:
                    print(f"第{i+1}次图像捕获失败")
                    
            except Exception as e:
                print(f"第{i+1}次多色检测出错: {str(e)}")
                
            time.sleep(0.2)
            gc.collect()
        
        print("✅ 多颜色检测完成")
        return True
        
    except Exception as e:
        print("❌ 多颜色检测失败:", str(e))
        return False

def main():
    """主函数"""
    print("🚀 K230修复版颜色检测器")
    print("解决 'sensor is already inited' 问题")
    
    # 清理现有传感器
    cleanup_sensor()
    
    # 安全初始化传感器
    sensor = safe_sensor_init()
    
    if sensor:
        print("\\n传感器初始化成功，开始测试...")
        
        # 测试传感器捕获
        if test_sensor_capture(sensor):
            print("\\n传感器捕获测试通过，开始颜色检测...")
            
            # 简单红色检测
            if simple_color_detection_fixed(sensor):
                print("\\n红色检测成功，开始多颜色检测...")
                # 多颜色检测
                multi_color_detection_fixed(sensor)
            else:
                print("红色检测失败，跳过多颜色检测")
        else:
            print("传感器捕获测试失败")
    else:
        print("❌ 传感器初始化失败")
        print("\\n可能的解决方案:")
        print("1. 重启K230开发板")
        print("2. 重新连接CanMV IDE")
        print("3. 检查摄像头连接")
        print("4. 尝试运行: exec(open('k230_simple_test.py').read())")
    
    print("\\n程序结束")

if __name__ == "__main__":
    main()
