import sensor, image, time, lcd
import gc

# 简化配置 - 所有参数统一管理
SIMPLE_CONFIG = {
    'target_color': 'red',  # 目标检测颜色
    'lcd_size': (320, 240),
    'min_pixels': 200,
    'rect_size': (60, 40),  # 生成矩形大小
    'colors': {
        'red': {'threshold': (30, 100, 15, 127, 15, 127), 'rgb': (255, 0, 0)},
        'green': {'threshold': (40, 80, -70, -10, -0, 30), 'rgb': (0, 255, 0)},
        'blue': {'threshold': (0, 30, 0, 64, -128, 0), 'rgb': (0, 0, 255)}
    }
}

def init_k230():
    """K230硬件初始化"""
    sensor.reset()
    sensor.set_pixformat(sensor.RGB565)
    sensor.set_framesize(sensor.QVGA)
    sensor.skip_frames(time=2000)
    lcd.init()
    lcd.clear()

def detect_target_color(img, color_name):
    """检测目标颜色"""
    color_info = SIMPLE_CONFIG['colors'][color_name]
    blobs = img.find_blobs([color_info['threshold']], pixels_threshold=SIMPLE_CONFIG['min_pixels'])
    return max(blobs, key=lambda b: b.pixels()) if blobs else None, color_info['rgb']

def draw_color_rectangle(img, center_x, center_y, color):
    """在指定位置绘制颜色矩形"""
    w, h = SIMPLE_CONFIG['rect_size']
    x = center_x - w//2
    y = center_y - h//2
    
    # 绘制填充矩形
    img.draw_rectangle((x, y, w, h), color=color, thickness=-1, fill=True)
    # 绘制边框
    img.draw_rectangle((x, y, w, h), color=(255, 255, 255), thickness=2)

def main_loop():
    """主循环"""
    clock = time.clock()
    target_color = SIMPLE_CONFIG['target_color']
    
    while True:
        clock.tick()
        img = sensor.snapshot()
        
        # 检测颜色
        blob, rgb_color = detect_target_color(img, target_color)
        
        if blob:
            # 在检测位置绘制矩形
            draw_color_rectangle(img, blob.cx(), blob.cy(), rgb_color)
            
            # 显示检测信息
            info = f"{target_color}: ({blob.cx()},{blob.cy()})"
            img.draw_string(10, 10, info, color=(255, 255, 255))
        
        # 显示FPS
        img.draw_string(10, 220, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
        
        lcd.display(img)
        gc.collect()

if __name__ == "__main__":
    print("K230简单颜色矩形生成器启动...")
    init_k230()
    main_loop()
