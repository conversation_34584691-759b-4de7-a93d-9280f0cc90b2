from machine import Pin, Timer
import time, gc
try:
    import sensor, image, lcd  # K230 CanMV库
except ImportError:
    print("警告: CanMV库未找到")

# MicroPython简化配置 - 所有参数统一管理
SIMPLE_CONFIG = {
    'target_color': 'red',  # 目标检测颜色
    'lcd_size': (320, 240),
    'min_pixels': 150,  # 降低最小像素要求
    'rect_size': (50, 35),  # 生成矩形大小
    'frame_delay': 50,  # 帧间延迟(ms)
    'colors': {
        'red': {'threshold': (30, 100, 15, 127, 15, 127), 'rgb': (255, 0, 0)},
        'green': {'threshold': (40, 80, -70, -10, -0, 30), 'rgb': (0, 255, 0)},
        'blue': {'threshold': (0, 30, 0, 64, -128, 0), 'rgb': (0, 0, 255)}
    }
}

def init_k230_micropython():
    """K230硬件初始化 - MicroPython版本"""
    try:
        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.QVGA)
        sensor.skip_frames(time=2000)
        lcd.init()
        lcd.clear()
        print("K230初始化成功")
        return True
    except Exception as e:
        print("初始化失败:", e)
        return False

def detect_target_color(img, color_name):
    """检测目标颜色 - MicroPython版本"""
    try:
        color_info = SIMPLE_CONFIG['colors'][color_name]
        blobs = img.find_blobs([color_info['threshold']],
                             pixels_threshold=SIMPLE_CONFIG['min_pixels'])
        if blobs:
            largest_blob = max(blobs, key=lambda b: b.pixels())
            return largest_blob, color_info['rgb']
    except Exception as e:
        print("颜色检测错误:", e)
    return None, SIMPLE_CONFIG['colors'][color_name]['rgb']

def draw_color_rectangle(img, center_x, center_y, color):
    """在指定位置绘制颜色矩形 - MicroPython版本"""
    try:
        w, h = SIMPLE_CONFIG['rect_size']
        x = max(0, center_x - w//2)  # 边界检查
        y = max(0, center_y - h//2)

        # 确保矩形不超出屏幕
        if x + w > SIMPLE_CONFIG['lcd_size'][0]:
            x = SIMPLE_CONFIG['lcd_size'][0] - w
        if y + h > SIMPLE_CONFIG['lcd_size'][1]:
            y = SIMPLE_CONFIG['lcd_size'][1] - h

        # 绘制填充矩形
        img.draw_rectangle((x, y, w, h), color=color, thickness=-1, fill=True)
        # 绘制边框
        img.draw_rectangle((x, y, w, h), color=(255, 255, 255), thickness=1)
    except Exception as e:
        print("绘制错误:", e)

def main_loop_micropython():
    """主循环 - MicroPython版本"""
    target_color = SIMPLE_CONFIG['target_color']
    frame_count = 0
    start_time = time.ticks_ms()

    print("开始检测颜色:", target_color)

    while True:
        try:
            img = sensor.snapshot()
            frame_count += 1

            # 检测颜色
            blob, rgb_color = detect_target_color(img, target_color)

            if blob:
                # 在检测位置绘制矩形
                draw_color_rectangle(img, blob.cx(), blob.cy(), rgb_color)

                # 显示检测信息 - MicroPython兼容格式
                info = "{}: ({},{})".format(target_color, blob.cx(), blob.cy())
                img.draw_string(10, 10, info, color=(255, 255, 255))

            # 计算并显示FPS
            if frame_count % 20 == 0:  # 每20帧计算一次FPS
                current_time = time.ticks_ms()
                elapsed = time.ticks_diff(current_time, start_time)
                fps = frame_count * 1000 / elapsed if elapsed > 0 else 0
                fps_text = "FPS: {:.1f}".format(fps)
                img.draw_string(10, 220, fps_text, color=(255, 255, 255))

            lcd.display(img)
            gc.collect()  # 内存管理
            time.sleep_ms(SIMPLE_CONFIG['frame_delay'])  # 控制帧率

        except KeyboardInterrupt:
            print("用户中断")
            break
        except Exception as e:
            print("运行错误:", e)
            time.sleep_ms(100)

if __name__ == "__main__":
    print("=== K230 MicroPython简单颜色矩形生成器 ===")
    if init_k230_micropython():
        main_loop_micropython()
    else:
        print("初始化失败，程序退出")
    print("程序结束")
