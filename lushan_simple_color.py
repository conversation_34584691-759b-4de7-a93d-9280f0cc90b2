# 庐山派K230简单颜色识别 - 轻量级版本
import time, gc

# 导入庐山派K230模块
try:
    from media.sensor import *
    from media.display import *
    from media.media import *
    print("✅ 庐山派K230模块加载成功")
except ImportError as e:
    print(f"❌ 模块加载失败: {e}")
    exit()

# 庐山派K230简化配置
COLORS = {
    'red': [(30, 100, 15, 127, 15, 127), (255, 0, 0), '红'],
    'green': [(40, 80, -70, -10, -0, 30), (0, 255, 0), '绿'],
    'blue': [(0, 30, 0, 64, -128, 0), (0, 0, 255), '蓝']
}

def init_lushan_camera():
    """初始化庐山派K230摄像头"""
    print("🔧 初始化庐山派K230摄像头...")
    
    try:
        # 清理资源
        try:
            MediaManager.deinit()
            Display.deinit()
        except:
            pass
        gc.collect()
        time.sleep(1)
        
        # 创建传感器 - 庐山派K230通常使用ID 0
        sensor = Sensor(id=0)
        sensor.reset()
        
        # 配置为最佳性能
        sensor.set_framesize(Sensor.QVGA)  # 320x240
        sensor.set_pixformat(Sensor.RGB565)
        
        # 初始化显示
        Display.init(Display.VIRT, 320, 240)
        
        # 初始化媒体管理器
        MediaManager.init()
        
        # 启动传感器
        sensor.run()
        
        print("✅ 庐山派K230摄像头初始化成功")
        time.sleep(2)
        return sensor
        
    except Exception as e:
        print(f"❌ 摄像头初始化失败: {str(e)}")
        
        # 尝试其他传感器ID
        for sensor_id in [1, 2]:
            try:
                print(f"尝试传感器ID: {sensor_id}")
                sensor = Sensor(id=sensor_id)
                sensor.reset()
                sensor.set_framesize(Sensor.QVGA)
                sensor.set_pixformat(Sensor.RGB565)
                Display.init(Display.VIRT, 320, 240)
                MediaManager.init()
                sensor.run()
                print(f"✅ 传感器ID {sensor_id} 初始化成功")
                time.sleep(2)
                return sensor
            except:
                continue
        
        return None

def simple_color_detection():
    """简单颜色检测"""
    print("🎨 庐山派K230简单颜色检测")
    
    # 初始化摄像头
    sensor = init_lushan_camera()
    if not sensor:
        print("❌ 摄像头初始化失败")
        return
    
    print("开始检测红、绿、蓝三种颜色...")
    print("请将彩色物体放在摄像头前")
    
    clock = time.clock()
    detection_count = {'red': 0, 'green': 0, 'blue': 0}
    
    try:
        for frame in range(50):  # 检测50帧
            clock.tick()
            
            # 捕获图像
            img = sensor.snapshot()
            if not img:
                continue
            
            detected_colors = []
            
            # 检测每种颜色
            for color_name, (threshold, rgb_color, cn_name) in COLORS.items():
                try:
                    # 查找颜色blob
                    blobs = img.find_blobs([threshold], pixels_threshold=100)
                    
                    # 处理检测到的blob
                    for blob in blobs[:1]:  # 每种颜色只取最大的一个
                        if blob.density() > 0.3:
                            # 绘制检测框
                            img.draw_rectangle(blob.rect(), color=rgb_color, thickness=2)
                            img.draw_cross(blob.cx(), blob.cy(), color=rgb_color, size=5, thickness=2)
                            
                            # 标签
                            label = f"{cn_name}({blob.cx()},{blob.cy()})"
                            img.draw_string(blob.x(), blob.y()-15, label, color=rgb_color, scale=1)
                            
                            detected_colors.append(color_name)
                            detection_count[color_name] += 1
                            
                except Exception as e:
                    print(f"检测{color_name}出错: {str(e)}")
                    continue
            
            # 绘制状态信息
            img.draw_string(5, 5, "庐山派K230颜色检测", color=(255, 255, 255), scale=1)
            img.draw_string(5, 20, f"帧数: {frame+1}/50", color=(255, 255, 255), scale=1)
            img.draw_string(5, 35, f"FPS: {clock.fps():.1f}", color=(255, 255, 255), scale=1)
            
            # 显示检测到的颜色数量
            y_offset = 50
            for color_name, count in detection_count.items():
                color_info = COLORS[color_name]
                rgb_color = color_info[1]
                cn_name = color_info[2]
                img.draw_string(5, y_offset, f"{cn_name}: {count}", color=rgb_color, scale=1)
                y_offset += 15
            
            # 在右侧绘制颜色块
            if detected_colors:
                block_x = 250
                for i, color_name in enumerate(set(detected_colors)):
                    color_info = COLORS[color_name]
                    rgb_color = color_info[1]
                    cn_name = color_info[2]
                    
                    y = 10 + i * 25
                    # 颜色块
                    img.draw_rectangle((block_x, y, 30, 20), color=rgb_color, thickness=-1, fill=True)
                    img.draw_rectangle((block_x, y, 30, 20), color=(255, 255, 255), thickness=1)
                    # 文字
                    img.draw_string(block_x+8, y+6, cn_name, color=(255, 255, 255), scale=1)
            
            # 显示图像
            Display.show_image(img)
            
            # 打印检测信息
            if detected_colors:
                colors_str = ', '.join([COLORS[c][2] for c in set(detected_colors)])
                print(f"第{frame+1}帧: 检测到 {colors_str}")
            
            time.sleep(0.1)
            
            # 内存管理
            if frame % 10 == 0:
                gc.collect()
    
    except Exception as e:
        print(f"❌ 检测过程出错: {str(e)}")
    
    # 显示最终统计
    print("\\n📊 检测统计结果:")
    total_detections = sum(detection_count.values())
    for color_name, count in detection_count.items():
        color_info = COLORS[color_name]
        cn_name = color_info[2]
        if total_detections > 0:
            percentage = (count / 50) * 100
            print(f"{cn_name}色: {count}次 ({percentage:.1f}%)")
        else:
            print(f"{cn_name}色: {count}次")
    
    print("✅ 庐山派K230颜色检测完成")

def test_lushan_camera():
    """测试庐山派K230摄像头"""
    print("🧪 测试庐山派K230摄像头功能...")
    
    sensor = init_lushan_camera()
    if not sensor:
        print("❌ 摄像头测试失败")
        return False
    
    try:
        for i in range(5):
            img = sensor.snapshot()
            if img:
                # 绘制测试图案
                img.draw_string(10, 10, f"庐山派K230测试 {i+1}/5", color=(255, 255, 255), scale=1)
                img.draw_rectangle((50, 50, 100, 80), color=(0, 255, 0), thickness=2)
                img.draw_cross(100, 90, color=(255, 0, 0), size=10, thickness=2)
                img.draw_circle(200, 100, 30, color=(0, 0, 255), thickness=2)
                
                # 显示分辨率信息
                img.draw_string(10, 200, f"分辨率: {img.width()}x{img.height()}", color=(255, 255, 255), scale=1)
                
                Display.show_image(img)
                print(f"✅ 第{i+1}次测试成功")
            else:
                print(f"❌ 第{i+1}次测试失败")
            
            time.sleep(0.5)
        
        print("✅ 庐山派K230摄像头测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 摄像头测试出错: {str(e)}")
        return False

def show_lushan_info():
    """显示庐山派K230信息"""
    print("""
🏔️ 庐山派K230颜色识别系统

📋 使用说明:
1. 运行摄像头测试: test_lushan_camera()
2. 运行颜色检测: simple_color_detection()

🎨 支持颜色:
- 红色 (Red)
- 绿色 (Green) 
- 蓝色 (Blue)

💡 使用技巧:
- 使用纯色物体进行测试
- 确保光照充足
- 物体大小建议手掌大小
- 背景尽量简单

🔧 如果遇到问题:
1. 检查摄像头连接
2. 重启庐山派K230开发板
3. 重新连接CanMV IDE
""")

def main():
    """主函数"""
    print("🏔️ 庐山派K230物体颜色识别系统")
    print("专为庐山派K230开发板设计")
    
    show_lushan_info()
    
    # 先测试摄像头
    if test_lushan_camera():
        print("\\n摄像头测试通过，开始颜色检测...")
        time.sleep(2)
        
        # 运行颜色检测
        simple_color_detection()
    else:
        print("\\n❌ 摄像头测试失败")
        print("请检查:")
        print("1. 摄像头连接")
        print("2. 庐山派K230固件")
        print("3. CanMV IDE连接")

if __name__ == "__main__":
    main()
