# MicroPython K230颜色检测配置文件示例
# 使用方法: 复制此文件为 mp_config.py 并根据需要修改参数

# 系统配置
SYSTEM_CONFIG = {
    'cpu_frequency': 400000000,  # CPU频率 (Hz)
    'memory_threshold': 50000,   # 内存阈值 (bytes)
    'gc_interval': 10,           # 垃圾回收间隔 (帧)
    'debug_mode': False          # 调试模式
}

# 显示配置
DISPLAY_CONFIG = {
    'width': 320,               # LCD宽度
    'height': 240,              # LCD高度
    'refresh_rate': 20,         # 刷新率 (FPS)
    'frame_delay': 50,          # 帧间延迟 (ms)
    'lcd_frequency': 15000000   # LCD频率
}

# 检测配置
DETECTION_CONFIG = {
    'min_area': 200,            # 最小检测区域 (像素)
    'max_objects': 3,           # 最大检测对象数
    'confidence_threshold': 0.6, # 置信度阈值
    'sensitivity': 0.5,         # 检测灵敏度
    'edge_margin': 10           # 边缘边距
}

# 视觉配置
VISUAL_CONFIG = {
    'rect_thickness': 2,        # 矩形边框粗细
    'cross_size': 6,           # 十字标记大小
    'font_scale': 1,           # 字体缩放
    'color_block_size': (35, 25), # 颜色块大小
    'label_offset': 12         # 标签偏移
}

# 颜色定义 - HSV阈值和显示配置
COLOR_DEFINITIONS = {
    'red': {
        'hsv_threshold': (30, 100, 15, 127, 15, 127),
        'rgb_color': (255, 0, 0),
        'chinese_name': '红色',
        'english_name': 'red',
        'enabled': True
    },
    'green': {
        'hsv_threshold': (40, 80, -70, -10, -0, 30),
        'rgb_color': (0, 255, 0),
        'chinese_name': '绿色',
        'english_name': 'green',
        'enabled': True
    },
    'blue': {
        'hsv_threshold': (0, 30, 0, 64, -128, 0),
        'rgb_color': (0, 0, 255),
        'chinese_name': '蓝色',
        'english_name': 'blue',
        'enabled': True
    },
    'yellow': {
        'hsv_threshold': (20, 100, -10, 10, 20, 127),
        'rgb_color': (255, 255, 0),
        'chinese_name': '黄色',
        'english_name': 'yellow',
        'enabled': True
    },
    'orange': {
        'hsv_threshold': (10, 100, 10, 127, 20, 127),
        'rgb_color': (255, 165, 0),
        'chinese_name': '橙色',
        'english_name': 'orange',
        'enabled': False  # 默认禁用，可根据需要启用
    },
    'purple': {
        'hsv_threshold': (120, 255, 120, 255, 0, 255),
        'rgb_color': (128, 0, 128),
        'chinese_name': '紫色',
        'english_name': 'purple',
        'enabled': False
    }
}

# 传感器配置
SENSOR_CONFIG = {
    'pixel_format': 'RGB565',   # 像素格式
    'frame_size': 'QVGA',       # 帧大小 (320x240)
    'skip_frames_time': 1500,   # 跳过帧时间 (ms)
    'auto_gain': False,         # 自动增益
    'gain_db': 8,              # 增益值 (dB)
    'auto_whitebal': False,     # 自动白平衡
    'auto_exposure': True       # 自动曝光
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    'enable_fps_limit': True,   # 启用FPS限制
    'max_fps': 25,             # 最大FPS
    'memory_optimization': True, # 内存优化
    'fast_detection': True,     # 快速检测模式
    'reduce_precision': False   # 降低精度以提升速度
}

# 用户界面配置
UI_CONFIG = {
    'show_fps': True,          # 显示FPS
    'show_memory': True,       # 显示内存使用
    'show_object_count': True, # 显示对象计数
    'show_coordinates': True,  # 显示坐标
    'chinese_labels': True,    # 使用中文标签
    'status_position': 'bottom' # 状态信息位置: 'top', 'bottom'
}

# 调试配置
DEBUG_CONFIG = {
    'print_fps': False,        # 打印FPS到控制台
    'print_memory': False,     # 打印内存使用
    'print_detections': False, # 打印检测结果
    'save_config': True,       # 保存配置到文件
    'verbose_errors': True     # 详细错误信息
}

# 导出完整配置
COMPLETE_CONFIG = {
    'system': SYSTEM_CONFIG,
    'display': DISPLAY_CONFIG,
    'detection': DETECTION_CONFIG,
    'visual': VISUAL_CONFIG,
    'colors': COLOR_DEFINITIONS,
    'sensor': SENSOR_CONFIG,
    'performance': PERFORMANCE_CONFIG,
    'ui': UI_CONFIG,
    'debug': DEBUG_CONFIG
}

def get_enabled_colors():
    """获取启用的颜色列表"""
    return {k: v for k, v in COLOR_DEFINITIONS.items() if v['enabled']}

def get_color_names(language='chinese'):
    """获取颜色名称列表"""
    enabled_colors = get_enabled_colors()
    if language == 'chinese':
        return [color['chinese_name'] for color in enabled_colors.values()]
    else:
        return [color['english_name'] for color in enabled_colors.values()]

def validate_config():
    """验证配置参数"""
    errors = []
    
    # 检查基本参数
    if DETECTION_CONFIG['max_objects'] > 5:
        errors.append("最大对象数不应超过5")
    
    if DISPLAY_CONFIG['refresh_rate'] > 30:
        errors.append("刷新率过高可能影响性能")
    
    if len(get_enabled_colors()) == 0:
        errors.append("至少需要启用一种颜色")
    
    return errors

def print_config_summary():
    """打印配置摘要"""
    print("=== MicroPython K230配置摘要 ===")
    print("启用颜色:", get_color_names('chinese'))
    print("最大对象数:", DETECTION_CONFIG['max_objects'])
    print("刷新率:", DISPLAY_CONFIG['refresh_rate'], "FPS")
    print("内存阈值:", SYSTEM_CONFIG['memory_threshold'], "bytes")
    
    errors = validate_config()
    if errors:
        print("配置警告:")
        for error in errors:
            print("-", error)
    else:
        print("配置验证通过")

if __name__ == "__main__":
    print_config_summary()
