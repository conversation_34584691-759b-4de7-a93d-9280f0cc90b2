# 🏔️ 一键修复庐山派K230传感器问题
# 专门解决"所有传感器ID初始化失败"错误

import time, gc

print("🏔️ 一键修复庐山派K230传感器问题")
print("=" * 50)
print("专门解决: ❌ 所有传感器ID初始化失败")
print("=" * 50)

def step1_check_modules():
    """步骤1: 检查模块"""
    print("\\n🔍 步骤1: 检查模块导入...")
    
    try:
        from media.sensor import *
        from media.display import *
        from media.media import *
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("\\n🔧 解决方案:")
        print("1. 重启庐山派K230开发板")
        print("2. 重新连接CanMV IDE")
        print("3. 检查固件版本")
        return False

def step2_force_cleanup():
    """步骤2: 强制清理"""
    print("\\n🧹 步骤2: 强制清理所有资源...")
    
    try:
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        # 停止所有传感器
        cleanup_count = 0
        for sensor_id in range(8):  # 尝试更多ID
            try:
                sensor = Sensor(id=sensor_id)
                sensor.stop()
                cleanup_count += 1
                print(f"✅ 清理传感器ID {sensor_id}")
            except Exception as e:
                if "not inited" not in str(e):
                    print(f"传感器ID {sensor_id}: {str(e)}")
        
        # 清理系统组件
        try:
            MediaManager.deinit()
            print("✅ 媒体管理器已清理")
        except: pass
        
        try:
            Display.deinit()
            print("✅ 显示已清理")
        except: pass
        
        # 强制垃圾回收
        for i in range(3):
            gc.collect()
            time.sleep(1)
        
        print(f"✅ 清理完成，共清理{cleanup_count}个传感器")
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {str(e)}")
        return False

def step3_find_sensor():
    """步骤3: 寻找可用传感器"""
    print("\\n🔍 步骤3: 寻找可用传感器...")
    
    try:
        from media.sensor import *
        
        # 尝试所有可能的传感器ID
        working_sensors = []
        
        for sensor_id in [0, 1, 2, 3, 4, 5]:
            try:
                print(f"测试传感器ID {sensor_id}...")
                sensor = Sensor(id=sensor_id)
                sensor.reset()
                working_sensors.append(sensor_id)
                print(f"✅ 传感器ID {sensor_id} 可用")
                
                # 立即停止避免占用
                sensor.stop()
                
            except Exception as e:
                error_msg = str(e)
                if "already inited" in error_msg:
                    print(f"⚠️ 传感器ID {sensor_id} 被占用")
                elif "not support" in error_msg:
                    print(f"❌ 传感器ID {sensor_id} 不支持")
                else:
                    print(f"❌ 传感器ID {sensor_id}: {error_msg}")
        
        if working_sensors:
            print(f"✅ 找到可用传感器: {working_sensors}")
            return working_sensors[0]
        else:
            print("❌ 没有找到可用传感器")
            return None
            
    except Exception as e:
        print(f"❌ 传感器搜索失败: {str(e)}")
        return None

def step4_test_init(sensor_id):
    """步骤4: 测试初始化"""
    print(f"\\n🚀 步骤4: 测试传感器ID {sensor_id} 初始化...")
    
    try:
        from media.sensor import *
        from media.display import *
        from media.media import *
        
        # 创建传感器
        sensor = Sensor(id=sensor_id)
        sensor.reset()
        print("✅ 传感器创建成功")
        
        # 最保守的配置
        sensor.set_framesize(Sensor.QQVGA)  # 160x120 最小分辨率
        sensor.set_pixformat(Sensor.RGB565)
        print("✅ 传感器配置成功")
        
        # 初始化显示
        Display.init(Display.VIRT, 160, 120)
        print("✅ 显示初始化成功")
        
        # 初始化媒体管理器
        MediaManager.init()
        print("✅ 媒体管理器初始化成功")
        
        # 启动传感器
        sensor.run()
        print("✅ 传感器启动成功")
        
        # 等待稳定
        time.sleep(3)
        print("✅ 系统稳定")
        
        return sensor
        
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        return None

def step5_test_capture(sensor):
    """步骤5: 测试图像捕获"""
    print("\\n📸 步骤5: 测试图像捕获...")
    
    if not sensor:
        print("❌ 传感器无效")
        return False
    
    try:
        success_count = 0
        
        for i in range(3):
            print(f"第{i+1}次捕获测试...")
            
            img = sensor.snapshot()
            if img:
                print(f"✅ 捕获成功: {img.width()}x{img.height()}")
                
                # 绘制测试图案
                img.draw_string(5, 5, f"庐山派{i+1}", color=(255, 255, 255), scale=1)
                img.draw_rectangle((10, 20, 50, 30), color=(0, 255, 0), thickness=2)
                img.draw_cross(35, 35, color=(255, 0, 0), size=5, thickness=2)
                
                # 显示
                Display.show_image(img)
                success_count += 1
                
            else:
                print(f"❌ 第{i+1}次捕获失败")
            
            time.sleep(1)
            gc.collect()
        
        if success_count > 0:
            print(f"✅ 图像捕获测试成功 ({success_count}/3)")
            return True
        else:
            print("❌ 图像捕获测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 捕获测试失败: {str(e)}")
        return False

def one_click_fix():
    """一键修复主流程"""
    print("🏔️ 开始一键修复庐山派K230...")
    
    # 步骤1: 检查模块
    if not step1_check_modules():
        print("\\n❌ 修复失败: 模块导入问题")
        print("请重启庐山派K230开发板后重试")
        return False
    
    # 步骤2: 强制清理
    if not step2_force_cleanup():
        print("\\n❌ 修复失败: 资源清理问题")
        return False
    
    # 等待资源释放
    print("\\n⏳ 等待资源释放...")
    time.sleep(3)
    
    # 步骤3: 寻找传感器
    sensor_id = step3_find_sensor()
    if sensor_id is None:
        print("\\n❌ 修复失败: 没有可用传感器")
        print("\\n🔧 建议:")
        print("1. 重启庐山派K230开发板")
        print("2. 检查摄像头连接线")
        print("3. 确认电源供电充足")
        return False
    
    # 步骤4: 测试初始化
    sensor = step4_test_init(sensor_id)
    if not sensor:
        print("\\n❌ 修复失败: 传感器初始化问题")
        return False
    
    # 步骤5: 测试捕获
    if not step5_test_capture(sensor):
        print("\\n⚠️ 部分成功: 初始化成功但捕获有问题")
        return True
    
    # 修复成功
    print("\\n🎉 一键修复完全成功!")
    print("✅ 庐山派K230硬件工作正常")
    print("✅ 传感器初始化成功")
    print("✅ 图像捕获正常")
    
    return True

def show_next_steps():
    """显示后续步骤"""
    print("\\n📋 后续步骤:")
    print("现在可以运行完整的颜色识别程序:")
    print()
    print("🥇 推荐: 庐山派K230简化版")
    print("exec(open('lushan_simple_color.py').read())")
    print()
    print("🥈 备选: 庐山派K230完整版")
    print("exec(open('lushan_k230_color_recognition.py').read())")
    print()
    print("🔧 如果还有问题:")
    print("1. 重启庐山派K230开发板")
    print("2. 重新运行一键修复")
    print("3. 检查硬件连接")

def main():
    """主函数"""
    print("🏔️ 庐山派K230一键修复工具")
    print("专门解决传感器初始化失败问题")
    
    print("\\n⚠️ 如果遇到以下错误:")
    print("❌ 所有传感器ID初始化失败")
    print("❌ 硬件初始化失败")
    print("❌ sensor(X) is already inited")
    print()
    print("🔧 本工具将自动修复这些问题")
    
    input("\\n按回车键开始修复...")
    
    if one_click_fix():
        show_next_steps()
    else:
        print("\\n❌ 一键修复失败")
        print("\\n🆘 紧急解决方案:")
        print("1. 重启庐山派K230开发板")
        print("2. 重新连接CanMV IDE")
        print("3. 重新运行此工具")
        print("4. 如果仍然失败，检查硬件连接")

if __name__ == "__main__":
    main()
