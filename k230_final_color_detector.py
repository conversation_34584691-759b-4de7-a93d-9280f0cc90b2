# K230最终修复版颜色检测器 - 解决所有语法和导入问题
import time, os, sys, gc

# 在模块级别导入，避免import *语法错误
try:
    from media.sensor import *
    from media.display import *
    from media.media import *
    MODULES_IMPORTED = True
    print("✅ K230模块导入成功")
except ImportError as e:
    print(f"❌ K230模块导入失败: {e}")
    MODULES_IMPORTED = False

# 正确的K230配置
K230_CONFIG = {
    'sensor': {
        'id': 2,  # CSI2接口，开发板默认摄像头
        'framesize': 'QVGA',  # 320x240分辨率，避免性能问题
        'pixformat': 'RGB565',  # RGB565格式
        'fps': 30  # 30帧每秒
    },
    'colors': {  # 颜色检测阈值 (LAB色彩空间)
        'red': [(30, 100, 15, 127, 15, 127), (255, 0, 0), '红色'],
        'green': [(40, 80, -70, -10, -0, 30), (0, 255, 0), '绿色'],
        'blue': [(0, 30, 0, 64, -128, 0), (0, 0, 255), '蓝色']
    },
    'detect': {
        'min_area': 100,  # 最小检测区域，降低要求
        'max_objects': 1  # 每种颜色最多检测1个对象，提高性能
    }
}

def safe_cleanup():
    """安全清理传感器资源"""
    if not MODULES_IMPORTED:
        return False
        
    print("🔧 清理传感器资源...")
    try:
        # 尝试停止可能存在的传感器
        sensor_ids = [0, 1, 2]
        for sensor_id in sensor_ids:
            try:
                temp_sensor = Sensor(id=sensor_id)
                temp_sensor.stop()
                print(f"✅ 停止传感器ID {sensor_id}")
            except:
                pass
        
        # 清理媒体管理器
        try:
            MediaManager.deinit()
            print("✅ 媒体管理器已清理")
        except:
            pass
        
        # 清理显示
        try:
            Display.deinit()
            print("✅ 显示已清理")
        except:
            pass
        
        # 垃圾回收
        gc.collect()
        time.sleep(1)
        return True
        
    except Exception as e:
        print(f"清理时出错: {str(e)}")
        return False

def safe_init_sensor():
    """安全初始化传感器"""
    if not MODULES_IMPORTED:
        print("❌ 模块未导入")
        return None
    
    # 先清理
    safe_cleanup()
    
    try:
        # 尝试不同的传感器ID
        sensor_ids = [2, 0, 1]
        sensor = None
        
        for sensor_id in sensor_ids:
            try:
                print(f"尝试传感器ID: {sensor_id}")
                sensor = Sensor(id=sensor_id)
                sensor.reset()
                print(f"✅ 传感器ID {sensor_id} 初始化成功")
                break
            except Exception as e:
                print(f"❌ 传感器ID {sensor_id} 失败: {str(e)}")
                continue
        
        if sensor is None:
            print("❌ 所有传感器ID都失败")
            return None
        
        # 配置传感器 - 使用字符串而不是常量
        if K230_CONFIG['sensor']['framesize'] == 'QVGA':
            sensor.set_framesize(Sensor.QVGA)
        elif K230_CONFIG['sensor']['framesize'] == 'VGA':
            sensor.set_framesize(Sensor.VGA)
        else:
            sensor.set_framesize(Sensor.QVGA)  # 默认
            
        if K230_CONFIG['sensor']['pixformat'] == 'RGB565':
            sensor.set_pixformat(Sensor.RGB565)
        else:
            sensor.set_pixformat(Sensor.RGB565)  # 默认
        
        # 初始化显示
        Display.init(Display.VIRT, sensor.width(), sensor.height())
        print("✅ 显示初始化成功")
        
        # 初始化媒体管理器
        MediaManager.init()
        print("✅ 媒体管理器初始化成功")
        
        # 启动传感器
        sensor.run()
        print("✅ 传感器启动成功")
        
        # 等待稳定
        time.sleep(2)
        print("✅ 传感器初始化完成")
        
        return sensor
        
    except Exception as e:
        print(f"❌ 传感器初始化失败: {str(e)}")
        return None

def test_basic_capture(sensor, test_count=3):
    """测试基本图像捕获"""
    print(f"\\n📸 测试图像捕获 ({test_count}次)...")
    
    success_count = 0
    for i in range(test_count):
        try:
            img = sensor.snapshot()
            if img:
                print(f"✅ 第{i+1}次捕获成功: {img.width()}x{img.height()}")
                
                # 简单绘制
                img.draw_string(10, 10, f"测试 {i+1}/{test_count}", color=(255, 255, 255), scale=1)
                img.draw_rectangle((50, 50, 60, 40), color=(0, 255, 0), thickness=2)
                
                # 显示
                Display.show_image(img)
                success_count += 1
            else:
                print(f"❌ 第{i+1}次捕获失败")
                
        except Exception as e:
            print(f"❌ 第{i+1}次捕获异常: {str(e)}")
        
        time.sleep(0.5)
        gc.collect()
    
    print(f"捕获测试结果: {success_count}/{test_count}")
    return success_count > 0

def simple_color_detection(sensor):
    """简单颜色检测"""
    print("\\n🎨 开始简单颜色检测...")
    
    try:
        # 红色阈值
        red_threshold = K230_CONFIG['colors']['red'][0]
        red_color = K230_CONFIG['colors']['red'][1]
        
        clock = time.clock()
        detection_count = 0
        
        for i in range(10):  # 检测10帧
            try:
                clock.tick()
                img = sensor.snapshot()
                
                if img:
                    # 检测红色
                    blobs = img.find_blobs([red_threshold], 
                                         pixels_threshold=K230_CONFIG['detect']['min_area'])
                    
                    detected_count = len(blobs)
                    if detected_count > 0:
                        detection_count += 1
                    
                    # 绘制检测结果
                    for blob in blobs[:K230_CONFIG['detect']['max_objects']]:
                        img.draw_rectangle(blob.rect(), color=red_color, thickness=2)
                        img.draw_cross(blob.cx(), blob.cy(), color=red_color, size=5, thickness=2)
                        
                        # 显示坐标
                        coord_text = f"({blob.cx()},{blob.cy()})"
                        img.draw_string(blob.x(), blob.y()-12, coord_text, color=red_color, scale=1)
                    
                    # 绘制状态
                    img.draw_string(10, 10, f"检测 {i+1}/10", color=(255, 255, 255), scale=1)
                    img.draw_string(10, 25, f"红色: {detected_count}个", color=(255, 255, 255), scale=1)
                    img.draw_string(10, 40, f"FPS: {clock.fps():.1f}", color=(255, 255, 255), scale=1)
                    
                    # 如果检测到红色，绘制矩形块
                    if detected_count > 0:
                        rect_x = sensor.width() - 50
                        rect_y = 10
                        img.draw_rectangle((rect_x, rect_y, 40, 20), color=red_color, thickness=-1, fill=True)
                        img.draw_rectangle((rect_x, rect_y, 40, 20), color=(255, 255, 255), thickness=1)
                        img.draw_string(rect_x+10, rect_y+6, "红", color=(255, 255, 255), scale=1)
                        
                        print(f"第{i+1}次检测到{detected_count}个红色对象")
                    
                    # 显示图像
                    Display.show_image(img)
                else:
                    print(f"第{i+1}次图像捕获失败")
                    
            except Exception as e:
                print(f"第{i+1}次检测出错: {str(e)}")
                
            time.sleep(0.3)
            gc.collect()
        
        print(f"\\n✅ 简单颜色检测完成")
        print(f"总共{10}帧中有{detection_count}帧检测到红色")
        return True
        
    except Exception as e:
        print(f"❌ 简单颜色检测失败: {str(e)}")
        return False

def multi_color_detection(sensor):
    """多颜色检测"""
    print("\\n🌈 开始多颜色检测...")
    
    try:
        clock = time.clock()
        
        for i in range(15):  # 检测15帧
            try:
                clock.tick()
                img = sensor.snapshot()
                
                if img:
                    detected_objects = []
                    
                    # 检测所有颜色
                    for color_name, (threshold, rgb_color, cn_name) in K230_CONFIG['colors'].items():
                        try:
                            blobs = img.find_blobs([threshold], 
                                                 pixels_threshold=K230_CONFIG['detect']['min_area'])
                            
                            for blob in blobs[:K230_CONFIG['detect']['max_objects']]:
                                if blob.density() > 0.3:
                                    detected_objects.append({
                                        'blob': blob,
                                        'rgb_color': rgb_color,
                                        'cn_name': cn_name
                                    })
                                    
                                    # 绘制检测框
                                    img.draw_rectangle(blob.rect(), color=rgb_color, thickness=2)
                                    img.draw_cross(blob.cx(), blob.cy(), color=rgb_color, size=5, thickness=2)
                                    
                                    # 显示标签
                                    label = f"{cn_name}({blob.cx()},{blob.cy()})"
                                    img.draw_string(blob.x(), blob.y()-12, label, color=rgb_color, scale=1)
                                    
                        except Exception as e:
                            print(f"检测{color_name}时出错: {str(e)}")
                            continue
                    
                    # 绘制状态信息
                    img.draw_string(10, 10, f"多色 {i+1}/15", color=(255, 255, 255), scale=1)
                    img.draw_string(10, 25, f"检测: {len(detected_objects)}个", color=(255, 255, 255), scale=1)
                    img.draw_string(10, 40, f"FPS: {clock.fps():.1f}", color=(255, 255, 255), scale=1)
                    
                    # 在右侧绘制颜色矩形块
                    rect_w, rect_h = 35, 18
                    start_x = sensor.width() - rect_w - 5
                    
                    for j, obj in enumerate(detected_objects[:3]):
                        y = 10 + j * (rect_h + 3)
                        rgb_color = obj['rgb_color']
                        cn_name = obj['cn_name']
                        
                        # 绘制填充矩形
                        img.draw_rectangle((start_x, y, rect_w, rect_h), 
                                         color=rgb_color, thickness=-1, fill=True)
                        # 绘制边框
                        img.draw_rectangle((start_x, y, rect_w, rect_h), 
                                         color=(255, 255, 255), thickness=1)
                        # 显示颜色名称
                        img.draw_string(start_x+8, y+5, cn_name, color=(255, 255, 255), scale=1)
                    
                    if detected_objects:
                        print(f"第{i+1}次检测到{len(detected_objects)}个颜色对象")
                    
                    # 显示图像
                    Display.show_image(img)
                else:
                    print(f"第{i+1}次图像捕获失败")
                    
            except Exception as e:
                print(f"第{i+1}次多色检测出错: {str(e)}")
                
            time.sleep(0.2)
            gc.collect()
        
        print("✅ 多颜色检测完成")
        return True
        
    except Exception as e:
        print("❌ 多颜色检测失败:", str(e))
        return False

def main():
    """主函数"""
    print("🚀 K230最终修复版颜色检测器")
    print("解决所有导入和语法问题")
    
    if not MODULES_IMPORTED:
        print("❌ K230模块导入失败，无法运行")
        print("请检查:")
        print("1. K230固件版本")
        print("2. CanMV IDE连接")
        print("3. 开发板状态")
        return
    
    # 安全初始化传感器
    sensor = safe_init_sensor()
    
    if sensor:
        print("\\n✅ 传感器初始化成功，开始测试...")
        
        # 测试基本捕获
        if test_basic_capture(sensor):
            print("\\n✅ 基本捕获测试通过，开始颜色检测...")
            
            # 简单颜色检测
            if simple_color_detection(sensor):
                print("\\n✅ 简单颜色检测成功，开始多颜色检测...")
                # 多颜色检测
                multi_color_detection(sensor)
            else:
                print("简单颜色检测失败")
        else:
            print("基本捕获测试失败")
    else:
        print("❌ 传感器初始化失败")
        print("\\n建议:")
        print("1. 重启K230开发板")
        print("2. 重新连接CanMV IDE")
        print("3. 检查摄像头连接")
    
    print("\\n程序结束")

if __name__ == "__main__":
    main()
