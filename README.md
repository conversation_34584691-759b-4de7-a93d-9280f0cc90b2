# K230颜色识别与矩形生成

## 项目简介
为K230开发板设计的颜色识别和矩形生成Python程序，支持实时检测多种颜色并生成对应颜色的矩形标记。

## 文件说明

### 1. color_detection.py - 基础颜色检测器
- **功能**: 基本的颜色识别和矩形绘制
- **特点**: 轻量级，适合初学者
- **支持颜色**: 红、绿、蓝、黄
- **使用方法**: 
  ```python
  python color_detection.py
  ```

### 2. advanced_color_detector.py - 高级颜色检测器  
- **功能**: 多颜色同时检测，生成彩色矩形块
- **特点**: 支持多对象检测，性能优化
- **支持颜色**: 红、绿、蓝、黄、橙
- **新增功能**:
  - 同时检测多个颜色对象
  - 右侧显示检测到的颜色矩形块
  - 实时FPS和对象计数显示
  - 自动生成配置文件
- **使用方法**:
  ```python
  python advanced_color_detector.py
  ```

### 3. simple_color_rect.py - 简化版矩形生成器
- **功能**: 单一颜色检测和矩形生成
- **特点**: 代码最简洁，专注核心功能
- **配置**: 可通过修改`SIMPLE_CONFIG['target_color']`切换检测颜色
- **使用方法**:
  ```python
  python simple_color_rect.py
  ```

## 配置说明

### 颜色阈值配置
所有颜色参数在各文件的CONFIG字典中统一管理：
- `hsv`: HSV颜色空间阈值 (色调, 饱和度, 明度)
- `rgb`: 显示颜色的RGB值
- `min_area`: 最小检测区域像素数

### 性能参数
- `FPS`: 帧率设置 (默认30fps)
- `LCD_WIDTH/HEIGHT`: 显示分辨率 (320x240)
- `RECT_THICKNESS`: 矩形边框粗细

## 硬件要求
- K230开发板
- 摄像头模块
- LCD显示屏

## 安装和运行
1. 将代码文件复制到K230开发板
2. 确保摄像头和LCD正常连接
3. 运行对应的Python文件

## 自定义颜色
修改配置文件中的颜色参数：
```python
'custom_color': {
    'hsv': (h_min, h_max, s_min, s_max, v_min, v_max),
    'rgb': (r, g, b),
    'name': '自定义颜色'
}
```

## 性能优化
- 使用`gc.collect()`进行内存管理
- 优化检测区域大小减少计算量
- 合理设置FPS避免过载

## 故障排除
1. **摄像头无图像**: 检查sensor初始化和连接
2. **检测不准确**: 调整HSV阈值参数
3. **性能问题**: 降低FPS或减少检测区域

## 更新日志
- v1.0: 基础颜色检测功能
- v1.1: 新增多颜色同时检测
- v1.2: 优化性能和用户界面
- v1.3: 添加配置文件管理和中文支持
