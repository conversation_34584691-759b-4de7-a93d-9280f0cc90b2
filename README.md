# K230颜色识别与矩形生成 (MicroPython版本)

## 项目简介
为K230开发板设计的MicroPython颜色识别和矩形生成程序，支持实时检测多种颜色并生成对应颜色的矩形标记。针对MicroPython环境进行了内存和性能优化。

## 文件说明

### MicroPython版本 (推荐)

### 1. color_detection.py - MicroPython基础颜色检测器
- **功能**: 基本的颜色识别和矩形绘制，MicroPython优化版本
- **特点**: 内存管理优化，错误处理完善
- **支持颜色**: 红、绿、蓝、黄
- **新增特性**:
  - 自动FPS计算和显示
  - 内存垃圾回收管理
  - 异常处理和恢复
- **使用方法**:
  ```python
  import color_detection
  # 或直接运行
  ```

### 2. micropython_color_detector.py - MicroPython高级颜色检测引擎
- **功能**: 多颜色同时检测，生成彩色矩形块，专为MicroPython优化
- **特点**:
  - CPU频率优化和内存管理
  - 支持多对象检测(最多3个)
  - 右侧显示检测到的颜色矩形块
  - 实时FPS、对象计数和内存使用显示
  - 中文标签支持
- **支持颜色**: 红、绿、蓝、黄
- **使用方法**:
  ```python
  import micropython_color_detector
  ```

### 3. simple_color_rect.py - MicroPython简化版矩形生成器
- **功能**: 单一颜色检测和矩形生成，MicroPython优化
- **特点**:
  - 代码最简洁，专注核心功能
  - 边界检查和错误处理
  - 内存优化和帧率控制
- **配置**: 可通过修改`SIMPLE_CONFIG['target_color']`切换检测颜色
- **使用方法**:
  ```python
  import simple_color_rect
  ```

### 4. lightweight_mp_detector.py - 超轻量级检测器
- **功能**: 最小化内存使用的单颜色检测器
- **特点**:
  - 极简代码，最低内存占用
  - 适合资源受限环境
  - 快速响应，稳定运行
- **使用方法**:
  ```python
  import lightweight_mp_detector
  ```

## MicroPython配置说明

### 颜色阈值配置
所有颜色参数在各文件的CONFIG字典中统一管理：
- `hsv`: HSV颜色空间阈值 (色调, 饱和度, 明度)
- `rgb`: 显示颜色的RGB值
- `cn`: 中文颜色名称
- `min_area`: 最小检测区域像素数

### MicroPython性能参数
- `FPS`: 帧率设置 (默认20fps，适配MicroPython)
- `cpu_freq`: CPU频率优化 (400MHz)
- `mem_threshold`: 内存阈值管理
- `gc_interval`: 垃圾回收间隔
- `refresh_ms`: 显示刷新间隔

### 内存优化特性
- 自动垃圾回收管理
- 内存使用监控
- 对象数量限制
- 帧率自适应控制

## 硬件要求
- K230开发板 (支持MicroPython/CanMV)
- 摄像头模块
- LCD显示屏

## 安装和运行
1. 确保K230开发板已安装MicroPython/CanMV固件
2. 将代码文件复制到开发板存储
3. 确保摄像头和LCD正常连接
4. 在MicroPython环境中导入并运行对应模块

## MicroPython自定义颜色
修改配置文件中的颜色参数：
```python
'custom_color': {
    'hsv': (h_min, h_max, s_min, s_max, v_min, v_max),
    'rgb': (r, g, b),
    'cn': '自定义',  # 中文名称
    'name': 'custom'
}
```

## MicroPython性能优化
- 使用`gc.collect()`进行自动内存管理
- CPU频率优化提升处理速度
- 限制同时检测对象数量
- 自适应帧率控制
- 内存阈值监控和清理

## MicroPython故障排除
1. **内存不足**:
   - 减少检测对象数量
   - 增加垃圾回收频率
   - 降低图像处理频率

2. **摄像头无图像**:
   - 检查CanMV库是否正确安装
   - 验证sensor初始化参数
   - 确认硬件连接

3. **检测不准确**:
   - 调整HSV阈值参数
   - 增加最小检测区域
   - 优化光照条件

4. **性能问题**:
   - 降低刷新率(增加delay)
   - 减少检测区域大小
   - 使用轻量级版本

## 版本选择建议
- **学习使用**: `lightweight_mp_detector.py`
- **一般应用**: `simple_color_rect.py`
- **高级功能**: `micropython_color_detector.py`
- **稳定性优先**: `color_detection.py`

## 更新日志
- v2.0: 完整MicroPython版本重构
- v2.1: 内存优化和性能提升
- v2.2: 中文支持和错误处理改进
- v2.3: 轻量级版本和多版本支持
