from machine import Pin, Timer
import time, gc, math
try:
    import sensor, image, lcd  # K230 CanMV库
    import ujson as json  # MicroPython JSON库
except ImportError:
    print("警告: 部分库未找到，使用兼容模式")
    json = None

# MicroPython高级配置文件
ADVANCED_CONFIG = {
    'DISPLAY': {'width': 320, 'height': 240, 'fps': 20},  # 降低帧率适配MicroPython
    'DETECTION': {'min_area': 250, 'max_objects': 3, 'confidence': 0.6},  # 减少对象数量
    'VISUAL': {'rect_thickness': 2, 'cross_size': 6, 'font_scale': 1},  # 简化视觉效果
    'TIMER': {'period_ms': 50, 'fps_update_ms': 1000},  # 定时器配置
    'COLORS': {
        'red': {'hsv': (30, 100, 15, 127, 15, 127), 'rgb': (255, 0, 0), 'name': '红色'},
        'green': {'hsv': (40, 80, -70, -10, -0, 30), 'rgb': (0, 255, 0), 'name': '绿色'},
        'blue': {'hsv': (0, 30, 0, 64, -128, 0), 'rgb': (0, 0, 255), 'name': '蓝色'},
        'yellow': {'hsv': (20, 100, -10, 10, 20, 127), 'rgb': (255, 255, 0), 'name': '黄色'},
        'orange': {'hsv': (10, 100, 10, 127, 20, 127), 'rgb': (255, 165, 0), 'name': '橙色'}
    }
}

class MicroPythonAdvancedColorDetector:
    def __init__(self):
        self.detected_objects = []  # 存储检测到的对象
        self.frame_count = 0
        self.last_fps_time = time.ticks_ms()
        self.fps = 0
        self.timer = None
        self.init_system()

    def init_system(self):
        """系统初始化 - MicroPython版本"""
        try:
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)
            sensor.skip_frames(time=2000)
            sensor.set_auto_gain(False, gain_db=8)  # 降低增益
            sensor.set_auto_whitebal(False)
            lcd.init(freq=15000000)
            lcd.clear()
            print("MicroPython高级颜色检测器初始化完成")
        except Exception as e:
            print("初始化错误:", e)
        
    def detect_multiple_colors(self, img):
        """同时检测多种颜色"""
        self.detected_objects.clear()
        
        for color_key, color_info in ADVANCED_CONFIG['COLORS'].items():
            blobs = img.find_blobs([color_info['hsv']], 
                                 pixels_threshold=ADVANCED_CONFIG['DETECTION']['min_area'],
                                 area_threshold=ADVANCED_CONFIG['DETECTION']['min_area'])
            
            for blob in blobs[:ADVANCED_CONFIG['DETECTION']['max_objects']]:
                if blob.density() > ADVANCED_CONFIG['DETECTION']['confidence']:
                    self.detected_objects.append({
                        'blob': blob, 'color_key': color_key, 'color_info': color_info
                    })
                    
    def draw_detection_results(self, img):
        """绘制检测结果"""
        for i, obj in enumerate(self.detected_objects):
            blob, color_info = obj['blob'], obj['color_info']
            
            # 绘制矩形框
            img.draw_rectangle(blob.rect(), color=color_info['rgb'], 
                             thickness=ADVANCED_CONFIG['VISUAL']['rect_thickness'])
            
            # 绘制中心十字
            img.draw_cross(blob.cx(), blob.cy(), color=color_info['rgb'], 
                          size=ADVANCED_CONFIG['VISUAL']['cross_size'], thickness=2)
            
            # 显示颜色标签
            label = f"{color_info['name']}({blob.pixels()})"
            text_y = blob.y() - 15 if blob.y() > 20 else blob.y() + blob.h() + 5
            img.draw_string(blob.x(), text_y, label, color=color_info['rgb'], 
                          scale=ADVANCED_CONFIG['VISUAL']['font_scale'])
            
    def draw_status_info(self, img, fps):
        """绘制状态信息"""
        # FPS显示
        img.draw_string(5, 5, f"FPS: {fps:.1f}", color=(255, 255, 255), scale=1)
        
        # 检测计数
        count_text = f"检测到: {len(self.detected_objects)}个对象"
        img.draw_string(5, ADVANCED_CONFIG['DISPLAY']['height']-35, count_text, 
                       color=(255, 255, 255), scale=1)
        
        # 帧计数
        img.draw_string(5, ADVANCED_CONFIG['DISPLAY']['height']-20, 
                       f"帧: {self.frame_count}", color=(255, 255, 255), scale=1)
        
    def generate_color_rectangles(self, img):
        """生成对应颜色的填充矩形"""
        rect_width, rect_height = 40, 30
        start_x = ADVANCED_CONFIG['DISPLAY']['width'] - rect_width - 10
        
        for i, obj in enumerate(self.detected_objects[:3]):  # 最多显示3个
            rect_y = 10 + i * (rect_height + 5)
            color_rgb = obj['color_info']['rgb']
            
            # 绘制填充矩形
            img.draw_rectangle((start_x, rect_y, rect_width, rect_height), 
                             color=color_rgb, thickness=-1, fill=True)
            
            # 绘制边框
            img.draw_rectangle((start_x, rect_y, rect_width, rect_height), 
                             color=(255, 255, 255), thickness=1)
                             
    def run_detection(self):
        """运行检测循环"""
        clock = time.clock()
        
        try:
            while True:
                clock.tick()
                img = sensor.snapshot()
                self.frame_count += 1
                
                # 执行颜色检测
                self.detect_multiple_colors(img)
                
                # 绘制检测结果
                self.draw_detection_results(img)
                
                # 生成颜色矩形
                self.generate_color_rectangles(img)
                
                # 显示状态信息
                self.draw_status_info(img, clock.fps())
                
                # 显示图像
                lcd.display(img)
                gc.collect()
                
        except KeyboardInterrupt:
            print("检测停止")
        except Exception as e:
            print(f"运行错误: {e}")
        finally:
            lcd.clear()

def create_color_config_file():
    """创建颜色配置文件"""
    config_data = {
        "colors": ADVANCED_CONFIG['COLORS'],
        "settings": {
            "min_area": ADVANCED_CONFIG['DETECTION']['min_area'],
            "max_objects": ADVANCED_CONFIG['DETECTION']['max_objects']
        }
    }
    
    try:
        with open('color_config.json', 'w') as f:
            json.dump(config_data, f, indent=2)
        print("颜色配置文件已创建: color_config.json")
    except:
        print("配置文件创建失败，使用默认配置")

def main():
    """主程序入口"""
    print("=== K230高级颜色检测器 ===")
    print("支持颜色:", [info['name'] for info in ADVANCED_CONFIG['COLORS'].values()])
    
    create_color_config_file()
    detector = AdvancedColorDetector()
    detector.run_detection()

if __name__ == "__main__":
    main()
