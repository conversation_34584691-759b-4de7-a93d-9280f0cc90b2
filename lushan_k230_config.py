# 庐山派K230专用配置文件
# 针对庐山派K230开发板的优化配置

# 硬件配置
LUSHAN_HARDWARE = {
    'board_name': '庐山派K230',
    'sensor_ids': [0, 1, 2],  # 庐山派K230支持的传感器ID
    'default_sensor_id': 0,   # 默认传感器ID
    'resolution': {
        'width': 320,
        'height': 240,
        'format': 'QVGA'  # 最佳性能分辨率
    },
    'pixel_format': 'RGB565',
    'fps': 30
}

# 颜色检测配置 - 针对庐山派K230优化
LUSHAN_COLORS = {
    'red': {
        'threshold': (30, 100, 15, 127, 15, 127),  # LAB阈值
        'rgb': (255, 0, 0),
        'name_cn': '红色',
        'name_en': 'Red',
        'min_area': 120
    },
    'green': {
        'threshold': (40, 80, -70, -10, -0, 30),
        'rgb': (0, 255, 0),
        'name_cn': '绿色',
        'name_en': 'Green',
        'min_area': 120
    },
    'blue': {
        'threshold': (0, 30, 0, 64, -128, 0),
        'rgb': (0, 0, 255),
        'name_cn': '蓝色',
        'name_en': 'Blue',
        'min_area': 120
    },
    'yellow': {
        'threshold': (60, 100, -10, 10, 20, 127),
        'rgb': (255, 255, 0),
        'name_cn': '黄色',
        'name_en': 'Yellow',
        'min_area': 150
    },
    'orange': {
        'threshold': (40, 70, 10, 50, 20, 80),
        'rgb': (255, 165, 0),
        'name_cn': '橙色',
        'name_en': 'Orange',
        'min_area': 150
    },
    'purple': {
        'threshold': (20, 60, 10, 60, -30, 20),
        'rgb': (128, 0, 128),
        'name_cn': '紫色',
        'name_en': 'Purple',
        'min_area': 150
    }
}

# 检测参数配置
LUSHAN_DETECTION = {
    'min_area': 100,           # 最小检测面积
    'max_objects_per_color': 2, # 每种颜色最多检测对象数
    'density_threshold': 0.3,   # 密度阈值
    'margin': 3,               # 检测框边距
    'confidence_threshold': 0.5 # 置信度阈值
}

# 显示配置
LUSHAN_DISPLAY = {
    'show_fps': True,
    'show_coordinates': True,
    'show_color_palette': True,
    'show_statistics': True,
    'font_scale': 1,
    'line_thickness': 2
}

# 性能配置
LUSHAN_PERFORMANCE = {
    'frame_skip': 0,           # 跳帧数量(0=不跳帧)
    'gc_interval': 10,         # 垃圾回收间隔(帧数)
    'sleep_time': 0.05,        # 帧间延时(秒)
    'max_detection_time': 30   # 最大检测时间(秒)
}

# 调试配置
LUSHAN_DEBUG = {
    'print_detection_info': True,
    'print_fps_info': True,
    'print_error_details': True,
    'save_debug_images': False
}

def get_color_config(color_name):
    """获取指定颜色的配置"""
    return LUSHAN_COLORS.get(color_name, None)

def get_all_color_names():
    """获取所有支持的颜色名称"""
    return list(LUSHAN_COLORS.keys())

def get_color_thresholds():
    """获取所有颜色的阈值列表"""
    return [config['threshold'] for config in LUSHAN_COLORS.values()]

def print_lushan_config():
    """打印庐山派K230配置信息"""
    print("🏔️ 庐山派K230配置信息")
    print("=" * 40)
    
    print(f"开发板: {LUSHAN_HARDWARE['board_name']}")
    print(f"分辨率: {LUSHAN_HARDWARE['resolution']['width']}x{LUSHAN_HARDWARE['resolution']['height']}")
    print(f"像素格式: {LUSHAN_HARDWARE['pixel_format']}")
    print(f"帧率: {LUSHAN_HARDWARE['fps']} FPS")
    
    print("\\n支持的颜色:")
    for color_name, config in LUSHAN_COLORS.items():
        print(f"- {config['name_cn']} ({config['name_en']})")
    
    print(f"\\n检测参数:")
    print(f"- 最小面积: {LUSHAN_DETECTION['min_area']} 像素")
    print(f"- 密度阈值: {LUSHAN_DETECTION['density_threshold']}")
    print(f"- 每色最大对象: {LUSHAN_DETECTION['max_objects_per_color']} 个")
    
    print("=" * 40)

if __name__ == "__main__":
    print_lushan_config()
