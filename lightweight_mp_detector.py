from machine import Pin
import time, gc
try:
    import sensor, image, lcd
except ImportError:
    print("CanMV库未找到")

# 轻量级配置 - 最小化内存使用
LITE_CONFIG = {
    'color': 'red',  # 单一颜色检测
    'area': 150,     # 最小区域
    'delay': 60,     # 帧延迟
    'rect': (40, 30), # 矩形大小
    'thresholds': {
        'red': (30, 100, 15, 127, 15, 127),
        'green': (40, 80, -70, -10, -0, 30),
        'blue': (0, 30, 0, 64, -128, 0)
    },
    'colors': {
        'red': (255, 0, 0),
        'green': (0, 255, 0), 
        'blue': (0, 0, 255)
    }
}

def init():
    """最小化初始化"""
    sensor.reset()
    sensor.set_pixformat(sensor.RGB565)
    sensor.set_framesize(sensor.QVGA)
    sensor.skip_frames(time=1000)
    lcd.init()
    lcd.clear()

def detect(img):
    """轻量级检测"""
    color = LITE_CONFIG['color']
    threshold = LITE_CONFIG['thresholds'][color]
    
    blobs = img.find_blobs([threshold], pixels_threshold=LITE_CONFIG['area'])
    return max(blobs, key=lambda b: b.pixels()) if blobs else None

def draw_rect(img, x, y, color):
    """绘制矩形"""
    w, h = LITE_CONFIG['rect']
    rect_x = max(0, min(320-w, x-w//2))
    rect_y = max(0, min(240-h, y-h//2))
    
    # 填充矩形
    img.draw_rectangle((rect_x, rect_y, w, h), color=color, fill=True)
    # 边框
    img.draw_rectangle((rect_x, rect_y, w, h), color=(255,255,255), thickness=1)

def main():
    """主程序"""
    print("轻量级MicroPython颜色检测器")
    init()
    
    color_name = LITE_CONFIG['color']
    color_rgb = LITE_CONFIG['colors'][color_name]
    frame = 0
    
    while True:
        try:
            img = sensor.snapshot()
            frame += 1
            
            blob = detect(img)
            if blob:
                draw_rect(img, blob.cx(), blob.cy(), color_rgb)
                info = "{}:({},{})".format(color_name, blob.cx(), blob.cy())
                img.draw_string(5, 5, info, color=(255,255,255))
            
            if frame % 20 == 0:  # 每20帧显示一次帧数
                img.draw_string(5, 220, "F:{}".format(frame), color=(255,255,255))
                gc.collect()
                
            lcd.display(img)
            time.sleep_ms(LITE_CONFIG['delay'])
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print("错误:", e)
            time.sleep_ms(100)
    
    lcd.clear()
    print("结束")

if __name__ == "__main__":
    main()
