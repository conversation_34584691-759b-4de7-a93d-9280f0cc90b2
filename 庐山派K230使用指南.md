# 🏔️ 庐山派K230物体颜色识别使用指南

## 🚀 快速开始

### 第一步：选择合适的版本

#### 🥇 推荐：庐山派K230完整版
```python
exec(open('lushan_k230_color_recognition.py').read())
```
- ✅ 专为庐山派K230优化
- ✅ 支持6种颜色检测
- ✅ 完整的统计功能
- ✅ 面向对象设计

#### 🥈 备选：庐山派K230简化版
```python
exec(open('lushan_simple_color.py').read())
```
- ✅ 轻量级，运行快速
- ✅ 支持3种基本颜色
- ✅ 代码简单易懂
- ✅ 内存占用小

## 🎨 支持的颜色

### 完整版支持的颜色
| 颜色 | 中文名 | 英文名 | RGB值 |
|------|--------|--------|-------|
| 🔴 | 红色 | Red | (255, 0, 0) |
| 🟢 | 绿色 | Green | (0, 255, 0) |
| 🔵 | 蓝色 | Blue | (0, 0, 255) |
| 🟡 | 黄色 | Yellow | (255, 255, 0) |
| 🟠 | 橙色 | Orange | (255, 165, 0) |
| 🟣 | 紫色 | Purple | (128, 0, 128) |

### 简化版支持的颜色
- 🔴 红色 (Red)
- 🟢 绿色 (Green)
- 🔵 蓝色 (Blue)

## 🔧 庐山派K230硬件配置

### 默认配置
- **传感器ID**: 0 (庐山派K230默认)
- **分辨率**: 320x240 (QVGA)
- **像素格式**: RGB565
- **帧率**: 30 FPS
- **最小检测面积**: 100-150像素

### 传感器ID说明
庐山派K230支持多个传感器ID，程序会自动尝试：
1. ID 0 - 默认摄像头
2. ID 1 - 备用摄像头1
3. ID 2 - 备用摄像头2

## 🚨 常见问题解决

### ❌ 问题1: 模块导入失败
```
❌ 模块加载失败: no module named 'media.sensor'
```
**解决方案**:
1. 检查庐山派K230固件版本
2. 确认CanMV IDE连接正常
3. 重启开发板后重试

### ❌ 问题2: 传感器初始化失败
```
❌ 摄像头初始化失败: sensor(0) is already inited
```
**解决方案**:
```python
# 重启庐山派K230开发板，然后运行
exec(open('lushan_simple_color.py').read())
```

### ❌ 问题3: 检测不到颜色
**解决方案**:
1. **光照条件**: 确保光线充足，避免阴影
2. **物体大小**: 使用手掌大小的彩色物体
3. **背景简单**: 使用白色或浅色背景
4. **颜色纯度**: 使用纯色物体，避免混合色

### ❌ 问题4: 程序运行缓慢
**解决方案**:
1. 使用简化版: `exec(open('lushan_simple_color.py').read())`
2. 减少检测颜色数量
3. 降低检测精度要求

## 💡 使用技巧

### 最佳检测条件
1. **光照**: 自然光或白色LED灯
2. **距离**: 摄像头前20-50cm
3. **背景**: 白色或浅色背景
4. **物体**: 纯色、较大的物体

### 颜色阈值调整
如果检测效果不理想，可以修改配置文件中的颜色阈值：

```python
# 在 lushan_k230_config.py 中修改
LUSHAN_COLORS = {
    'red': {
        'threshold': (30, 100, 15, 127, 15, 127),  # LAB阈值
        'min_area': 120  # 最小面积
    }
}
```

### 性能优化建议
1. **使用简化版**进行快速测试
2. **调整检测面积**阈值
3. **减少同时检测的颜色数量**
4. **适当增加帧间延时**

## 📊 检测结果说明

### 显示信息
- **FPS**: 实时帧率
- **检测框**: 彩色矩形框标识物体
- **坐标**: 物体中心坐标
- **颜色块**: 右侧显示检测到的颜色
- **统计**: 各颜色检测次数

### 输出信息
```
第25帧: 检测到 红色, 蓝色
📊 检测统计结果:
红色: 15次 (30.0%)
绿色: 8次 (16.0%)
蓝色: 12次 (24.0%)
```

## 🔬 测试和调试

### 基础功能测试
```python
# 测试庐山派K230摄像头
exec(open('lushan_simple_color.py').read())
# 然后调用: test_lushan_camera()
```

### 配置信息查看
```python
# 查看庐山派K230配置
exec(open('lushan_k230_config.py').read())
```

### 调试模式
在配置文件中启用调试模式：
```python
LUSHAN_DEBUG = {
    'print_detection_info': True,
    'print_fps_info': True,
    'print_error_details': True
}
```

## 🛠️ 高级配置

### 自定义颜色
在配置文件中添加新颜色：
```python
LUSHAN_COLORS['pink'] = {
    'threshold': (20, 80, 15, 45, -10, 30),
    'rgb': (255, 192, 203),
    'name_cn': '粉色',
    'name_en': 'Pink',
    'min_area': 120
}
```

### 性能调优
```python
LUSHAN_PERFORMANCE = {
    'frame_skip': 1,           # 跳帧提高性能
    'gc_interval': 5,          # 更频繁的垃圾回收
    'sleep_time': 0.1,         # 增加帧间延时
    'max_detection_time': 15   # 减少检测时间
}
```

## 📞 技术支持

### 如果遇到问题：
1. **重启设备**: 重启庐山派K230开发板
2. **重新连接**: 重新连接CanMV IDE
3. **检查硬件**: 确认摄像头连接正常
4. **运行测试**: 先运行简化版测试
5. **查看日志**: 注意控制台输出的错误信息

### 推荐的故障排除流程：
```python
# 1. 基础测试
exec(open('lushan_simple_color.py').read())

# 2. 如果基础测试通过，运行完整版
exec(open('lushan_k230_color_recognition.py').read())

# 3. 如果还有问题，检查配置
exec(open('lushan_k230_config.py').read())
```

## ✅ 成功运行的标志

当程序正常运行时，你会看到：
- ✅ 庐山派K230模块加载成功
- ✅ 庐山派K230摄像头初始化成功
- ✅ 实时图像显示
- ✅ 彩色检测框出现在物体上
- ✅ 右侧显示颜色调色板
- ✅ 控制台输出检测统计

## 🎉 项目特色

### 专为庐山派K230优化
- 🏔️ 针对庐山派K230硬件特性优化
- 🚀 高性能的颜色检测算法
- 🎨 丰富的颜色支持
- 📊 详细的统计和分析
- 🔧 灵活的配置系统
- 💡 简单易用的接口

祝你在庐山派K230上玩得愉快！🎉
