# K230 CanMV正确API颜色检测器 - 基于官方文档
import time, os, sys
from media.sensor import *  # 导入sensor模块，使用摄像头相关接口
from media.display import *  # 导入display模块，使用display相关接口
from media.media import *  # 导入media模块，使用media相关接口

# 正确的K230配置
K230_CONFIG = {
    'sensor': {
        'id': 2,  # CSI2接口，开发板默认摄像头
        'framesize': Sensor.VGA,  # 640x480分辨率
        'pixformat': Sensor.RGB565,  # RGB565格式
        'fps': 30  # 30帧每秒
    },
    'colors': {  # 颜色检测阈值 (LAB色彩空间)
        'red': [(30, 100, 15, 127, 15, 127), (255, 0, 0), '红色'],
        'green': [(40, 80, -70, -10, -0, 30), (0, 255, 0), '绿色'],
        'blue': [(0, 30, 0, 64, -128, 0), (0, 0, 255), '蓝色']
    },
    'detect': {
        'min_area': 200,  # 最小检测区域
        'max_objects': 2  # 每种颜色最多检测2个对象
    },
    'display': {
        'rect_size': (50, 25),  # 颜色矩形块大小
        'margin': 10  # 边距
    }
}

class K230CorrectColorDetector:
    def __init__(self):
        self.sensor = None
        self.clock = None
        self.frame_count = 0
        self.detected_colors = []
        
    def init_hardware(self):
        """初始化K230硬件 - 使用正确的API"""
        try:
            # 创建传感器对象
            self.sensor = Sensor(id=K230_CONFIG['sensor']['id'])
            print("✅ 传感器对象创建成功")
            
            # 复位和初始化摄像头
            self.sensor.reset()
            print("✅ 摄像头复位成功")
            
            # 设置帧大小
            self.sensor.set_framesize(K230_CONFIG['sensor']['framesize'])
            print("✅ 帧大小设置成功")
            
            # 设置像素格式
            self.sensor.set_pixformat(K230_CONFIG['sensor']['pixformat'])
            print("✅ 像素格式设置成功")
            
            # 使用IDE缓冲区输出图像
            Display.init(Display.VIRT, self.sensor.width(), self.sensor.height())
            print("✅ 显示初始化成功")
            
            # 初始化media资源管理器
            MediaManager.init()
            print("✅ Media管理器初始化成功")
            
            # 启动传感器
            self.sensor.run()
            print("✅ 传感器启动成功")
            
            # 创建时钟对象
            self.clock = time.clock()
            print("✅ 时钟对象创建成功")
            
            # 等待摄像头稳定
            time.sleep(2)
            print("✅ K230硬件初始化完成")
            return True
            
        except Exception as e:
            print("❌ 硬件初始化失败:", str(e))
            return False
            
    def detect_colors_in_image(self, img):
        """在图像中检测颜色"""
        self.detected_colors.clear()
        
        for color_name, (threshold, rgb_color, cn_name) in K230_CONFIG['colors'].items():
            try:
                # 使用find_blobs检测颜色
                blobs = img.find_blobs([threshold], 
                                     pixels_threshold=K230_CONFIG['detect']['min_area'])
                
                # 处理检测到的色块
                for blob in blobs[:K230_CONFIG['detect']['max_objects']]:
                    if blob.density() > 0.3:  # 密度过滤
                        self.detected_colors.append({
                            'blob': blob,
                            'color_name': color_name,
                            'rgb_color': rgb_color,
                            'cn_name': cn_name
                        })
                        
            except Exception as e:
                print(f"检测{color_name}时出错:", str(e))
                continue
                
    def draw_detection_results(self, img):
        """绘制检测结果"""
        for obj in self.detected_colors:
            blob = obj['blob']
            rgb_color = obj['rgb_color']
            cn_name = obj['cn_name']
            
            try:
                # 绘制矩形框
                img.draw_rectangle(blob.rect(), color=rgb_color, thickness=2)
                
                # 绘制中心点
                img.draw_cross(blob.cx(), blob.cy(), color=rgb_color, size=6, thickness=2)
                
                # 显示标签
                label = f"{cn_name}({blob.cx()},{blob.cy()})"
                img.draw_string(blob.x(), blob.y()-12, label, color=rgb_color, scale=1)
                
            except Exception as e:
                print("绘制检测结果出错:", str(e))
                continue
                
    def generate_color_rectangles(self, img):
        """生成颜色矩形块"""
        rect_w, rect_h = K230_CONFIG['display']['rect_size']
        margin = K230_CONFIG['display']['margin']
        start_x = self.sensor.width() - rect_w - margin
        
        for i, obj in enumerate(self.detected_colors[:3]):  # 最多显示3个
            y = margin + i * (rect_h + 5)
            rgb_color = obj['rgb_color']
            cn_name = obj['cn_name']
            
            try:
                # 绘制填充矩形
                img.draw_rectangle((start_x, y, rect_w, rect_h), 
                                 color=rgb_color, thickness=-1, fill=True)
                
                # 绘制白色边框
                img.draw_rectangle((start_x, y, rect_w, rect_h), 
                                 color=(255, 255, 255), thickness=1)
                
                # 显示颜色名称
                img.draw_string(start_x+3, y+6, cn_name, color=(255, 255, 255), scale=1)
                
            except Exception as e:
                print("生成颜色矩形出错:", str(e))
                continue
                
    def draw_status_info(self, img):
        """绘制状态信息"""
        try:
            # FPS显示
            fps = self.clock.fps()
            fps_text = f"FPS: {fps:.1f}"
            img.draw_string(10, 10, fps_text, color=(255, 255, 255), scale=1)
            
            # 帧数显示
            frame_text = f"帧数: {self.frame_count}"
            img.draw_string(10, 25, frame_text, color=(255, 255, 255), scale=1)
            
            # 检测数量
            count_text = f"检测: {len(self.detected_colors)}个"
            img.draw_string(10, 40, count_text, color=(255, 255, 255), scale=1)
            
        except Exception as e:
            print("绘制状态信息出错:", str(e))
            
    def run_detection_loop(self):
        """运行检测循环"""
        print("开始颜色检测循环...")
        
        try:
            while True:
                self.clock.tick()  # 开始计时
                
                # 拍摄一张图片
                img = self.sensor.snapshot()
                if img is None:
                    print("图像捕获失败")
                    continue
                    
                self.frame_count += 1
                
                # 执行颜色检测
                self.detect_colors_in_image(img)
                
                # 绘制检测结果
                self.draw_detection_results(img)
                
                # 生成颜色矩形块
                self.generate_color_rectangles(img)
                
                # 绘制状态信息
                self.draw_status_info(img)
                
                # 显示图片
                Display.show_image(img)
                
                # 打印FPS（可选）
                if self.frame_count % 30 == 0:  # 每30帧打印一次
                    print(f"FPS: {self.clock.fps():.1f}")
                
        except KeyboardInterrupt:
            print("用户停止检测")
        except Exception as e:
            print("检测循环出错:", str(e))

def simple_red_detection():
    """简单红色检测测试"""
    print("启动简单红色检测测试...")
    
    try:
        # 创建传感器
        sensor = Sensor(id=2)  # 使用默认摄像头
        sensor.reset()
        sensor.set_framesize(Sensor.QVGA)  # 320x240
        sensor.set_pixformat(Sensor.RGB565)
        
        # 初始化显示
        Display.init(Display.VIRT, sensor.width(), sensor.height())
        MediaManager.init()
        sensor.run()
        
        # 等待稳定
        time.sleep(2)
        
        # 红色阈值
        red_threshold = (30, 100, 15, 127, 15, 127)
        
        print("简单红色检测开始...")
        clock = time.clock()
        
        for i in range(30):  # 检测30帧
            clock.tick()
            img = sensor.snapshot()
            
            if img:
                # 检测红色
                blobs = img.find_blobs([red_threshold], pixels_threshold=100)
                
                if blobs:
                    largest = max(blobs, key=lambda b: b.pixels())
                    print(f"检测到红色: 位置({largest.cx()}, {largest.cy()}) 大小{largest.pixels()}")
                    
                    # 绘制检测框
                    img.draw_rectangle(largest.rect(), color=(255, 0, 0), thickness=2)
                    
                    # 绘制红色矩形块
                    img.draw_rectangle((250, 10, 40, 20), color=(255, 0, 0), thickness=-1, fill=True)
                    img.draw_rectangle((250, 10, 40, 20), color=(255, 255, 255), thickness=1)
                    img.draw_string(255, 16, "红", color=(255, 255, 255), scale=1)
                
                # 显示帧信息
                img.draw_string(10, 10, f"测试 {i+1}/30", color=(255, 255, 255), scale=1)
                img.draw_string(10, 25, f"FPS: {clock.fps():.1f}", color=(255, 255, 255), scale=1)
                
                # 显示图像
                Display.show_image(img)
                
            time.sleep(0.1)
        
        print("✅ 简单红色检测完成")
        return True
        
    except Exception as e:
        print("❌ 简单红色检测失败:", str(e))
        return False

def test_k230_basic_functions():
    """测试K230基本功能"""
    print("=== K230基本功能测试 ===")
    
    try:
        # 测试传感器创建
        sensor = Sensor(id=2)
        print("✅ 传感器创建成功")
        
        # 测试复位
        sensor.reset()
        print("✅ 传感器复位成功")
        
        # 测试设置
        sensor.set_framesize(Sensor.QVGA)
        sensor.set_pixformat(Sensor.RGB565)
        print("✅ 传感器配置成功")
        
        # 测试显示初始化
        Display.init(Display.VIRT, 320, 240)
        print("✅ 显示初始化成功")
        
        # 测试媒体管理器
        MediaManager.init()
        print("✅ 媒体管理器初始化成功")
        
        print("✅ K230基本功能测试通过")
        return True
        
    except Exception as e:
        print("❌ K230基本功能测试失败:", str(e))
        return False

def main():
    """主函数"""
    print("=== K230 CanMV正确API颜色检测器 ===")
    
    # 基本功能测试
    if test_k230_basic_functions():
        print("基本功能测试通过，启动完整检测器...")
        
        try:
            # 创建检测器
            detector = K230CorrectColorDetector()
            
            # 初始化硬件
            if detector.init_hardware():
                # 运行检测循环
                detector.run_detection_loop()
            else:
                print("硬件初始化失败，尝试简单测试...")
                simple_red_detection()
                
        except Exception as e:
            print("完整检测器出错:", str(e))
            print("尝试简单测试...")
            simple_red_detection()
    else:
        print("基本功能测试失败")
        print("请检查:")
        print("1. K230固件版本")
        print("2. 摄像头连接")
        print("3. CanMV IDE连接")

if __name__ == "__main__":
    main()
