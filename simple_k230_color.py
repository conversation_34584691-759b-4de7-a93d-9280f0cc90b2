# K230 CanMV简化颜色检测器
from media.sensor import *  # K230传感器模块
from media.display import *  # K230显示模块  
from media.media import *  # K230媒体模块
import time, gc
import image

# 简化配置
CONFIG = {
    'colors': {  # 颜色配置：[HSV阈值, RGB颜色, 中文名]
        'red': [(30, 100, 15, 127, 15, 127), (255, 0, 0), '红'],
        'green': [(40, 80, -70, -10, -0, 30), (0, 255, 0), '绿'],
        'blue': [(0, 30, 0, 64, -128, 0), (0, 0, 255), '蓝']
    },
    'min_area': 300,  # 最小检测区域
    'rect_size': (60, 40)  # 生成矩形大小
}

def init_k230():
    """初始化K230硬件"""
    try:
        # 初始化媒体
        media_init()
        
        # 创建传感器
        sensor = Sensor(id=0)
        sensor.reset()
        sensor.set_framesize(width=640, height=480)
        sensor.set_pixformat(PIXEL_FORMAT_RGB_888_PLANAR)
        
        # 创建显示
        display = Display()
        display.init(lt9611_1920x1080_30fps)
        
        # 绑定传感器和显示
        bind_info = sensor.bind_info()
        display.bind_layer(**bind_info, layer=Display.LAYER_VIDEO1)
        
        # 启动传感器
        sensor.run()
        
        print("K230初始化成功")
        return sensor, display
        
    except Exception as e:
        print("初始化失败:", str(e))
        return None, None

def detect_and_draw(img, color_name, hsv_threshold, rgb_color, cn_name):
    """检测并绘制指定颜色"""
    try:
        # 检测颜色
        blobs = img.find_blobs([hsv_threshold], pixels_threshold=CONFIG['min_area'])
        
        if blobs:
            # 找到最大色块
            largest = max(blobs, key=lambda b: b.pixels())
            
            # 绘制检测框
            img.draw_rectangle(largest.rect(), color=rgb_color, thickness=3)
            
            # 绘制中心点
            img.draw_cross(largest.cx(), largest.cy(), color=rgb_color, size=8, thickness=2)
            
            # 显示标签
            label = f"{cn_name}:({largest.cx()},{largest.cy()})"
            img.draw_string(largest.x(), largest.y()-25, label, color=rgb_color, scale=2)
            
            return largest
            
    except Exception as e:
        print(f"检测{color_name}出错:", str(e))
        
    return None

def draw_color_rect(img, x, y, width, height, color, name):
    """绘制颜色矩形块"""
    try:
        # 填充矩形
        img.draw_rectangle((x, y, width, height), color=color, thickness=-1, fill=True)
        # 白色边框
        img.draw_rectangle((x, y, width, height), color=(255, 255, 255), thickness=2)
        # 颜色名称
        img.draw_string(x+5, y+10, name, color=(255, 255, 255), scale=2)
    except Exception as e:
        print("绘制矩形出错:", str(e))

def simple_detection_loop():
    """简化检测循环"""
    sensor, display = init_k230()
    
    if not sensor or not display:
        print("硬件初始化失败")
        return
        
    print("开始颜色检测...")
    frame_count = 0
    
    try:
        while True:
            # 获取图像
            rgb888_img = sensor.snapshot()
            if rgb888_img is None:
                continue
                
            # 转换为RGB565进行处理
            img = rgb888_img.to_rgb565()
            frame_count += 1
            
            detected_colors = []  # 存储检测到的颜色
            
            # 检测所有配置的颜色
            for color_name, (hsv, rgb, cn) in CONFIG['colors'].items():
                blob = detect_and_draw(img, color_name, hsv, rgb, cn)
                if blob:
                    detected_colors.append((rgb, cn))
                    
            # 在右侧绘制检测到的颜色矩形块
            rect_w, rect_h = CONFIG['rect_size']
            start_x = 640 - rect_w - 10
            
            for i, (color, name) in enumerate(detected_colors[:3]):  # 最多3个
                y = 10 + i * (rect_h + 10)
                draw_color_rect(img, start_x, y, rect_w, rect_h, color, name)
                
            # 显示状态信息
            img.draw_string(10, 10, f"帧数: {frame_count}", color=(255, 255, 255), scale=2)
            img.draw_string(10, 40, f"检测: {len(detected_colors)}个", color=(255, 255, 255), scale=2)
            
            # 显示图像
            display.show_image(rgb888_img)
            
            # 内存管理
            gc.collect()
            time.sleep_ms(50)  # 控制帧率
            
    except KeyboardInterrupt:
        print("用户停止")
    except Exception as e:
        print("检测循环出错:", str(e))
    finally:
        # 清理资源
        try:
            sensor.stop()
            display.deinit()
            media_deinit()
            print("资源清理完成")
        except:
            pass

def test_basic_functions():
    """测试基本功能"""
    print("=== K230基本功能测试 ===")
    
    try:
        # 测试媒体初始化
        media_init()
        print("✅ 媒体初始化成功")
        
        # 测试传感器
        sensor = Sensor(id=0)
        sensor.reset()
        print("✅ 传感器创建成功")
        
        # 测试显示
        display = Display()
        print("✅ 显示模块创建成功")
        
        # 清理
        media_deinit()
        print("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        print("❌ 基本功能测试失败:", str(e))
        return False

def red_only_detector():
    """仅检测红色的最简版本"""
    print("启动红色检测器...")
    
    try:
        media_init()
        sensor = Sensor(id=0)
        sensor.reset()
        sensor.set_framesize(width=640, height=480)
        sensor.set_pixformat(PIXEL_FORMAT_RGB_888_PLANAR)
        
        display = Display()
        display.init(lt9611_1920x1080_30fps)
        
        bind_info = sensor.bind_info()
        display.bind_layer(**bind_info, layer=Display.LAYER_VIDEO1)
        sensor.run()
        
        red_threshold = (30, 100, 15, 127, 15, 127)  # 红色阈值
        
        for i in range(50):  # 检测50帧
            rgb888_img = sensor.snapshot()
            if rgb888_img is None:
                continue
                
            img = rgb888_img.to_rgb565()
            
            # 检测红色
            blobs = img.find_blobs([red_threshold], pixels_threshold=200)
            
            if blobs:
                largest = max(blobs, key=lambda b: b.pixels())
                # 绘制检测框
                img.draw_rectangle(largest.rect(), color=(255, 0, 0), thickness=3)
                # 绘制红色矩形块
                img.draw_rectangle((550, 10, 60, 40), color=(255, 0, 0), thickness=-1, fill=True)
                img.draw_rectangle((550, 10, 60, 40), color=(255, 255, 255), thickness=2)
                img.draw_string(555, 20, "红", color=(255, 255, 255), scale=2)
                
            img.draw_string(10, 10, f"红色检测 {i+1}/50", color=(255, 255, 255), scale=2)
            display.show_image(rgb888_img)
            time.sleep_ms(100)
            
        print("红色检测完成")
        
    except Exception as e:
        print("红色检测出错:", str(e))
    finally:
        try:
            sensor.stop()
            display.deinit()
            media_deinit()
        except:
            pass

def main():
    """主函数"""
    print("=== K230 CanMV颜色检测器 ===")
    
    # 测试基本功能
    if test_basic_functions():
        print("开始完整颜色检测...")
        simple_detection_loop()
    else:
        print("基本功能测试失败，尝试红色检测...")
        red_only_detector()

if __name__ == "__main__":
    main()
